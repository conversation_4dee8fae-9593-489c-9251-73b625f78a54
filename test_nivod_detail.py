# -*- coding: utf-8 -*-
"""
测试泥视频爬虫的detailContent方法
验证播放列表获取功能是否正常
"""

import sys

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_nivod_detail():
    """测试泥视频详情功能"""
    print("=" * 80)
    print("🔍 测试泥视频爬虫detailContent方法")
    print("=" * 80)
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider
        spider = Spider()
        spider.init()
        print("✅ 泥视频爬虫初始化成功")
    except Exception as e:
        print(f"❌ 泥视频爬虫初始化失败: {str(e)}")
        return
    
    # 1. 先搜索获取一个有效的视频ID
    print("\n1. 搜索获取测试视频ID")
    print("-" * 50)
    
    try:
        search_result = spider.searchContent("斗罗大陆", False, "1")
        if search_result and 'list' in search_result and search_result['list']:
            test_video = search_result['list'][0]
            test_id = test_video['vod_id']
            test_name = test_video['vod_name']
            print(f"✅ 找到测试视频: {test_name} (ID: {test_id})")
        else:
            print("❌ 搜索未找到测试视频，使用默认ID")
            test_id = "84898"  # 使用之前测试过的ID
            test_name = "测试视频"
    except Exception as e:
        print(f"❌ 搜索失败: {str(e)}")
        test_id = "84898"
        test_name = "测试视频"
    
    # 2. 测试detailContent方法
    print(f"\n2. 测试detailContent方法 - ID: {test_id}")
    print("-" * 50)
    
    try:
        detail_result = spider.detailContent([test_id])
        print(f"detailContent返回结果类型: {type(detail_result)}")
        
        if detail_result and 'list' in detail_result:
            videos = detail_result['list']
            print(f"返回视频数量: {len(videos)}")
            
            if videos:
                video = videos[0]
                print(f"视频信息:")
                print(f"  ID: {video.get('vod_id', 'N/A')}")
                print(f"  标题: {video.get('vod_name', 'N/A')}")
                print(f"  图片: {video.get('vod_pic', 'N/A')}")
                print(f"  年份: {video.get('vod_year', 'N/A')}")
                print(f"  地区: {video.get('vod_area', 'N/A')}")
                print(f"  简介: {video.get('vod_content', 'N/A')[:100]}...")
                
                # 重点检查播放源信息
                play_from = video.get('vod_play_from', '')
                play_url = video.get('vod_play_url', '')
                
                print(f"\n📺 播放源信息分析:")
                print(f"  vod_play_from: {play_from}")
                print(f"  vod_play_url长度: {len(play_url)}")
                
                if play_from:
                    sources = play_from.split('$$$')
                    print(f"  播放源数量: {len(sources)}")
                    for i, source in enumerate(sources):
                        print(f"    {i+1}. {source}")
                    
                    if play_url:
                        urls = play_url.split('$$$')
                        print(f"  播放URL组数量: {len(urls)}")
                        
                        # 分析第一个播放源的剧集
                        if urls:
                            first_episodes = urls[0].split('#')
                            print(f"  第一个播放源剧集数: {len(first_episodes)}")
                            
                            # 显示前几集
                            for i, episode in enumerate(first_episodes[:5]):
                                if '$' in episode:
                                    ep_name, ep_url = episode.split('$', 1)
                                    print(f"    第{i+1}集: {ep_name} -> {ep_url}")
                                else:
                                    print(f"    第{i+1}集: {episode}")
                        
                        print("✅ 播放列表获取成功")
                    else:
                        print("❌ 播放URL为空")
                        print("🔍 这是关键问题：vod_play_url为空")
                else:
                    print("❌ 播放源为空")
                    print("🔍 这是关键问题：vod_play_from为空")
            else:
                print("❌ 返回的视频列表为空")
        else:
            print("❌ detailContent返回格式错误")
            
    except Exception as e:
        print(f"❌ detailContent测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试_extract_detail_info方法
    print(f"\n3. 直接测试_extract_detail_info方法")
    print("-" * 50)
    
    try:
        # 获取详情页面HTML
        detail_url = f"{spider.host}/nivod/{test_id}/"
        print(f"详情页面URL: {detail_url}")
        
        rsp = spider.fetch(detail_url, headers=spider.headers)
        if rsp and rsp.status_code == 200:
            doc = spider.html(rsp.text)
            
            # 直接调用_extract_detail_info方法
            vod_info = spider._extract_detail_info(doc, test_id)
            
            if vod_info:
                print(f"_extract_detail_info返回:")
                print(f"  标题: {vod_info.get('vod_name', 'N/A')}")
                print(f"  播放源: {vod_info.get('vod_play_from', 'N/A')}")
                print(f"  播放URL: {len(vod_info.get('vod_play_url', ''))}")
                
                # 直接测试_extract_play_sources方法
                play_from, play_url = spider._extract_play_sources(doc, test_id)
                print(f"\n_extract_play_sources返回:")
                print(f"  play_from: {play_from}")
                print(f"  play_url数量: {len(play_url)}")
                
                if not play_from:
                    print("❌ _extract_play_sources返回空播放源")
                    
                    # 分析页面结构
                    print(f"\n🔍 页面结构分析:")
                    play_links = doc.xpath('//a[contains(@href, "/niplay/")]')
                    print(f"  找到niplay链接数量: {len(play_links)}")
                    
                    if play_links:
                        for i, link in enumerate(play_links[:5]):
                            href = link.get('href', '')
                            text = link.text or ''
                            print(f"    链接{i+1}: {text} -> {href}")
                    else:
                        print("  未找到niplay链接")
                        
                        # 查找其他可能的播放相关元素
                        all_links = doc.xpath('//a/@href')
                        play_related = [link for link in all_links if 'play' in link.lower()]
                        print(f"  包含'play'的链接数量: {len(play_related)}")
                        for link in play_related[:5]:
                            print(f"    {link}")
                            
                        # 查找可能的播放按钮或链接
                        buttons = doc.xpath('//button | //div[contains(@class, "play")] | //span[contains(@class, "play")]')
                        print(f"  可能的播放按钮数量: {len(buttons)}")
                        
                        # 查看页面文本内容
                        page_text = ' '.join(doc.xpath('//text()'))
                        if '播放' in page_text:
                            print("  页面包含'播放'文本")
                        if '集' in page_text:
                            print("  页面包含'集'文本")
                else:
                    print("✅ _extract_play_sources方法工作正常")
            else:
                print("❌ _extract_detail_info返回None")
        else:
            print(f"❌ 详情页面请求失败: {rsp.status_code if rsp else 'None'}")
            
    except Exception as e:
        print(f"❌ _extract_detail_info测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 4. 总结分析
    print(f"\n4. 问题分析总结")
    print("-" * 50)
    
    print("🔍 泥视频detailContent方法检查项:")
    print("1. ✅ 方法存在且可调用")
    print("2. ✅ _extract_detail_info方法存在")
    print("3. ✅ _extract_play_sources方法存在")
    print("4. ❓ 播放源提取逻辑需要验证")
    
    print("\n💡 可能的问题:")
    print("1. 网站HTML结构可能发生变化")
    print("2. XPath选择器可能需要更新")
    print("3. 播放链接格式可能不匹配")
    print("4. 播放源提取逻辑可能有缺陷")

if __name__ == '__main__':
    test_nivod_detail()
