# PyramidStore爬虫开发流程指南

## 🎯 开发前准备

### 1. 环境检查
```bash
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 验证环境
python -c "import requests, lxml, pyquery; print('环境正常')"

# 进入开发目录
cd plugin
```

### 2. 规范学习
- 📖 **必读**: `PyramidStore爬虫开发规范.md`
- 📋 **约束**: `模块清单.md` - 严格限制使用模块
- 🔧 **基类**: `base/spider.py` - 了解可用方法
- 📝 **参考**: 现有优秀爬虫实现

## 🔍 第一阶段：网站分析

### 1.1 使用web-fetch工具分析
```python
# 分析目标网站
from web_fetch import fetch_page
content = fetch_page("https://target-site.com")
print(content)
```

### 1.2 关键信息收集
- **网站类型**: APP接口 / HTML页面 / 混合模式
- **URL模式**: 首页、分类、搜索、详情、播放页面的URL规律
- **数据格式**: JSON API / HTML解析
- **技术特点**: 懒加载、加密、反爬虫机制
- **分类体系**: 支持的影视分类和筛选条件

### 1.3 创建分析文档
```markdown
# 网站分析报告

## 基本信息
- 网站名称: xxx
- 主域名: https://xxx.com
- 网站类型: APP接口/HTML页面

## URL模式
- 首页: /
- 分类: /category/{type}/{page}
- 搜索: /search?q={keyword}
- 详情: /detail/{id}
- 播放: /play/{id}

## 技术特点
- 图片懒加载: 是/否
- 数据加密: 是/否
- 反爬虫: 是/否

## 分类信息
1. 电影 - type_id: 1
2. 电视剧 - type_id: 2
...
```

## 🏗️ 第二阶段：架构设计

### 2.1 确定爬虫类型

#### APP类型特征
- 调用移动应用API
- 返回JSON格式数据
- 可能需要特殊headers
- 可能需要加密解密

#### HTML类型特征  
- 解析网页HTML内容
- 使用XPath选择器
- 需要处理懒加载等问题
- 可能需要模拟浏览器行为

### 2.2 技术方案选择
```python
# APP类型示例
class Spider(Spider):
    def init(self, extend=""):
        self.host = "https://api.example.com"
        self.headers = {
            'User-Agent': 'App/1.0',
            'platform': 'android'
        }

# HTML类型示例  
class Spider(Spider):
    def init(self, extend=""):
        self.host = "https://www.example.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 ...'
        }
```

### 2.3 方法规划
- `homeContent`: 分类定义 + 首页推荐
- `categoryContent`: 分类内容获取
- `searchContent`: 搜索功能实现
- `detailContent`: 详情页面解析
- `playerContent`: 播放链接获取

## 🔨 第三阶段：基础开发

### 3.1 创建爬虫文件
```python
# -*- coding: utf-8 -*-
import sys
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def getName(self):
        return "网站名称"
    
    def init(self, extend=""):
        self.host = "https://example.com"
        # 其他初始化代码
        pass
    
    # 必需的空方法
    def isVideoFormat(self, url): pass
    def manualVideoCheck(self): pass  
    def destroy(self): pass
```

### 3.2 实现核心方法

#### 步骤1: 实现homeContent
```python
def homeContent(self, filter):
    result = {}
    
    # 1. 定义分类（必需）
    classes = [
        {'type_name': '电影', 'type_id': '1'},
        {'type_name': '电视剧', 'type_id': '2'},
        # 根据网站实际分类添加
    ]
    result['class'] = classes
    
    # 2. 获取首页推荐（可选）
    try:
        # 实现首页数据获取逻辑
        videos = self._get_home_videos()
        result['list'] = videos
    except Exception as e:
        self.log(f"首页获取出错: {str(e)}")
        result['list'] = []
    
    return result
```

#### 步骤2: 实现searchContent
```python
def searchContent(self, key, quick, pg='1'):
    try:
        # 构建搜索请求
        search_url = f"{self.host}/search?q={key}&page={pg}"
        rsp = self.fetch(search_url, headers=self.headers)
        
        # 解析搜索结果
        videos = self._parse_search_results(rsp)
        
        return {
            'list': videos,
            'page': int(pg)
        }
    except Exception as e:
        self.log(f"搜索出错: {str(e)}")
        return {'list': []}
```

#### 步骤3: 实现其他方法
按照规范文档中的标准格式实现剩余方法。

### 3.3 提取通用方法
```python
def _extract_videos(self, elements):
    """通用视频信息提取"""
    videos = []
    seen_ids = set()
    
    for element in elements:
        video_info = self._extract_video_info(element)
        if video_info and video_info['vod_id'] not in seen_ids:
            videos.append(video_info)
            seen_ids.add(video_info['vod_id'])
    
    return videos

def _extract_video_info(self, element):
    """提取单个视频信息"""
    try:
        vod_id = self._extract_id(element)
        title = self._extract_title(element)
        pic = self._extract_image(element)
        remarks = self._extract_remarks(element)
        
        return {
            'vod_id': vod_id,
            'vod_name': title,
            'vod_pic': pic,
            'vod_remarks': remarks
        }
    except Exception as e:
        self.log(f"视频信息提取出错: {str(e)}")
        return None
```

## 🧪 第四阶段：测试验证

### 4.1 功能测试
```python
if __name__ == '__main__':
    spider = Spider()
    spider.init()
    
    print("=== 功能测试开始 ===")
    
    # 测试首页
    print("\n1. 测试首页内容")
    home_result = spider.homeContent({})
    print(f"   分类数量: {len(home_result.get('class', []))}")
    print(f"   推荐视频: {len(home_result.get('list', []))}")
    
    # 测试分类
    print("\n2. 测试分类内容")
    if home_result.get('class'):
        first_class = home_result['class'][0]
        category_result = spider.categoryContent(first_class['type_id'], '1', {}, {})
        print(f"   分类视频: {len(category_result.get('list', []))}")
    
    # 测试搜索
    print("\n3. 测试搜索功能")
    search_result = spider.searchContent("测试", False)
    print(f"   搜索结果: {len(search_result.get('list', []))}")
    
    # 测试详情
    print("\n4. 测试详情页面")
    if search_result.get('list'):
        first_video = search_result['list'][0]
        detail_result = spider.detailContent([first_video['vod_id']])
        print(f"   详情信息: {'正常' if detail_result.get('list') else '异常'}")
    
    print("\n=== 功能测试完成 ===")
```

### 4.2 性能测试
```python
import time

def performance_test():
    spider = Spider()
    spider.init()
    
    print("=== 性能测试开始 ===")
    
    # 测试各功能耗时
    tests = [
        ('首页内容', lambda: spider.homeContent({})),
        ('分类内容', lambda: spider.categoryContent('1', '1', {}, {})),
        ('搜索功能', lambda: spider.searchContent('测试', False)),
    ]
    
    total_time = 0
    for test_name, test_func in tests:
        start_time = time.time()
        try:
            result = test_func()
            end_time = time.time()
            elapsed = end_time - start_time
            total_time += elapsed
            
            status = "✅" if elapsed < 3 else "⚠️" if elapsed < 5 else "❌"
            print(f"{status} {test_name}: {elapsed:.2f}秒")
        except Exception as e:
            print(f"❌ {test_name}: 出错 - {str(e)}")
    
    # 性能评级
    print(f"\n总耗时: {total_time:.2f}秒")
    if total_time < 3:
        print("🚀 性能评级: 卓越")
    elif total_time < 8:
        print("⭐ 性能评级: 优秀") 
    elif total_time < 15:
        print("✅ 性能评级: 达标")
    else:
        print("❌ 性能评级: 需要优化")
    
    print("=== 性能测试完成 ===")

if __name__ == '__main__':
    performance_test()
```

### 4.3 数据验证
```python
def validate_data():
    spider = Spider()
    spider.init()
    
    print("=== 数据验证开始 ===")
    
    # 验证首页数据
    home_result = spider.homeContent({})
    
    # 检查分类格式
    classes = home_result.get('class', [])
    for cls in classes:
        assert 'type_name' in cls, "分类缺少type_name字段"
        assert 'type_id' in cls, "分类缺少type_id字段"
    print("✅ 分类格式验证通过")
    
    # 检查视频格式
    videos = home_result.get('list', [])
    for video in videos[:3]:  # 检查前3个
        required_fields = ['vod_id', 'vod_name', 'vod_pic']
        for field in required_fields:
            assert field in video, f"视频缺少{field}字段"
        
        # 检查图片URL格式
        if video['vod_pic']:
            assert video['vod_pic'].startswith('http'), "图片URL必须是完整HTTP地址"
    print("✅ 视频格式验证通过")
    
    print("=== 数据验证完成 ===")

if __name__ == '__main__':
    validate_data()
```

## 🚀 第五阶段：优化完善

### 5.1 性能优化
- **减少重复请求**: 使用缓存机制
- **优化选择器**: 使用高效的XPath表达式
- **并发处理**: 合理使用异步请求
- **错误处理**: 完善异常处理机制

### 5.2 代码精简
- **提取通用方法**: 避免重复代码
- **简化逻辑**: 减少不必要的复杂度
- **优化数据结构**: 使用高效的数据处理方式

### 5.3 质量提升
- **添加注释**: 为关键逻辑添加中文注释
- **统一命名**: 使用清晰的方法和变量名
- **错误日志**: 完善的错误记录机制

## 📋 交付检查清单

### 功能完整性
- [ ] 首页内容正常显示
- [ ] 所有分类可以浏览
- [ ] 搜索功能准确有效
- [ ] 详情页面信息完整
- [ ] 播放链接格式正确

### 性能标准
- [ ] 所有方法响应时间 < 5秒
- [ ] 总体性能达到优秀标准
- [ ] 内存使用合理

### 代码质量
- [ ] 严格遵循开发规范
- [ ] 代码结构清晰
- [ ] 注释完整
- [ ] 错误处理完善

### 兼容性
- [ ] 100%兼容TVBox标准
- [ ] 配置文件格式正确
- [ ] 数据格式符合规范

---

**遵循此流程可以确保开发出高质量、高性能的PyramidStore爬虫。**
