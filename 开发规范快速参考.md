# PyramidStore爬虫开发快速参考

## 🚫 严格约束 (必须遵守)

### 模块限制
```python
# ✅ 允许使用的模块 (仅限这些)
import re, json, time, os, sys, base64, uuid, threading
import requests, lxml, pyquery
from Crypto.Cipher import AES
from Crypto.Hash import MD5, SHA1
import aiohttp, concurrent.futures

# ❌ 禁止引入任何其他模块
```

### 架构约束
```python
# ✅ 必须继承Spider基类
from base.spider import Spider
class Spider(Spider):
    pass

# ❌ 禁止修改基类或创建其他基类
```

## 📋 标准类结构

```python
# -*- coding: utf-8 -*-
import sys
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def getName(self):
        return "爬虫名称"
    
    def init(self, extend=""):
        self.host = "https://example.com"
        pass
    
    # 必需的空方法
    def isVideoFormat(self, url): pass
    def manualVideoCheck(self): pass
    def destroy(self): pass
    
    # 核心方法
    def homeContent(self, filter): pass
    def categoryContent(self, tid, pg, filter, extend): pass
    def detailContent(self, ids): pass
    def searchContent(self, key, quick, pg='1'): pass
    def playerContent(self, flag, id, vipFlags): pass
```

## 📊 标准返回格式

### homeContent
```python
{
    'class': [
        {'type_name': '电影', 'type_id': '1'},
        {'type_name': '电视剧', 'type_id': '2'}
    ],
    'filters': {...},  # 可选
    'list': [...]      # 可选
}
```

### categoryContent
```python
{
    'list': [...],
    'page': int(pg),
    'pagecount': 999,
    'limit': 20,
    'total': 999
}
```

### detailContent
```python
{
    'list': [{
        'vod_id': '视频ID',
        'vod_name': '标题',
        'vod_pic': '图片URL',
        'vod_year': '年份',
        'vod_content': '简介',
        'vod_play_from': '播放源1$$$播放源2',
        'vod_play_url': '第1集$链接1#第2集$链接2$$$...'
    }]
}
```

### searchContent
```python
{
    'list': [...],
    'page': int(pg)
}
```

### playerContent
```python
{
    'parse': 0,      # 0=直链 1=需要解析
    'url': '播放链接',
    'header': {...}  # 可选
}
```

## 🎯 性能标准

| 级别 | 响应时间 | 评级 |
|------|----------|------|
| 卓越 | < 1秒 | 🚀 |
| 优秀 | < 3秒 | ⭐ |
| 达标 | < 5秒 | ✅ |
| 不合格 | > 5秒 | ❌ |

## 💡 常用代码模板

### 通用视频提取
```python
def _extract_videos(self, elements):
    videos = []
    seen_ids = set()
    
    for element in elements:
        try:
            vod_id = self.regStr(r'/detail/(\d+)', element.xpath('.//a/@href')[0])
            if vod_id in seen_ids:
                continue
            seen_ids.add(vod_id)
            
            title = element.xpath('.//a/@title')[0].strip()
            pic = self._format_url(element.xpath('.//img/@src')[0])
            
            videos.append({
                'vod_id': vod_id,
                'vod_name': title,
                'vod_pic': pic,
                'vod_remarks': ''
            })
        except:
            continue
    
    return videos
```

### 懒加载图片处理
```python
def _extract_image(self, element):
    selectors = ['.//img/@data-original', './/img/@data-src', './/img/@src']
    
    for selector in selectors:
        pics = element.xpath(selector)
        for p in pics:
            if p and not p.endswith('blank.gif') and 'base64' not in p:
                return self._format_url(p)
    return ''

def _format_url(self, url):
    if url.startswith('//'):
        return 'https:' + url
    elif url.startswith('/'):
        return self.host + url
    elif url.startswith('http'):
        return url
    return ''
```

### 智能搜索过滤
```python
def _filter_search_results(self, videos, search_key):
    filtered = []
    search_key_lower = search_key.lower()
    
    for video in videos:
        title_lower = video['vod_name'].lower()
        if search_key_lower in title_lower:
            filtered.append(video)
    
    return filtered
```

### 错误处理模板
```python
def methodName(self, params):
    try:
        # 主要逻辑
        result = self._do_something(params)
        return result
    except Exception as e:
        self.log(f"方法名出错: {str(e)}")
        return {'list': []}  # 返回空结果而不是抛出异常
```

## 🔧 调试测试

### 快速测试
```python
if __name__ == '__main__':
    spider = Spider()
    spider.init()
    
    # 测试首页
    home = spider.homeContent({})
    print(f"分类: {len(home.get('class', []))}")
    
    # 测试搜索
    search = spider.searchContent("测试", False)
    print(f"搜索: {len(search.get('list', []))}")
```

### 性能测试
```python
import time

start = time.time()
result = spider.homeContent({})
elapsed = time.time() - start

if elapsed < 1:
    print("🚀 卓越")
elif elapsed < 3:
    print("⭐ 优秀")
elif elapsed < 5:
    print("✅ 达标")
else:
    print("❌ 需要优化")
```

## 📁 文件组织

```
plugin/
├── app/           # APP类型爬虫
├── html/          # HTML类型爬虫  
├── official/      # 官方平台爬虫
└── tools/         # 工具脚本
```

## 🎓 学习参考

### 优秀案例
- **APP类型**: `美帕APP.py` (简单API)
- **HTML类型**: `播剧网_优化版.py` (完整解析)
- **高性能**: `统一影视_精简版.py` (性能优化)

### 必读文档
- `PyramidStore爬虫开发规范.md` - 完整规范
- `爬虫开发流程指南.md` - 开发流程
- `模块清单.md` - 模块约束

## ⚠️ 常见错误

### 1. 模块使用错误
```python
# ❌ 错误
import beautifulsoup4  # 未在允许列表中

# ✅ 正确  
from pyquery import PyQuery as pq
```

### 2. 返回格式错误
```python
# ❌ 错误
return videos  # 直接返回列表

# ✅ 正确
return {'list': videos}  # 返回标准格式
```

### 3. 图片URL错误
```python
# ❌ 错误
'vod_pic': '/images/pic.jpg'  # 相对路径

# ✅ 正确
'vod_pic': 'https://example.com/images/pic.jpg'  # 完整URL
```

### 4. 异常处理缺失
```python
# ❌ 错误
def searchContent(self, key, quick):
    data = self.fetch(url).json()  # 可能抛出异常
    return data

# ✅ 正确
def searchContent(self, key, quick):
    try:
        data = self.fetch(url).json()
        return {'list': data}
    except Exception as e:
        self.log(f"搜索出错: {str(e)}")
        return {'list': []}
```

---

**快速参考完毕，详细内容请查看完整规范文档。**
