# -*- coding: utf-8 -*-
"""
对比统一影视和泥视频在TVBox中的播放差异
重点分析可能导致TVBox跳转失败的因素
"""

import sys
import re
import requests
import urllib.parse
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def compare_tvbox_playback():
    """对比TVBox播放差异"""
    print("=" * 120)
    print("🎬 TVBox播放差异对比分析")
    print("=" * 120)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入爬虫
    try:
        from 统一影视 import Spider as UnitySpider
        from 泥视频 import Spider as NivodSpider
        
        unity_spider = UnitySpider()
        unity_spider.init()
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        print("✅ 爬虫初始化成功")
        print(f"统一影视: {unity_spider.host}")
        print(f"泥视频: {nivod_spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试相同类型的视频
    unity_id = "73268"  # 斗罗大陆II绝世唐门
    nivod_id = "82135"  # 斗罗大陆II绝世唐门
    
    print(f"\n📺 测试视频对比:")
    print(f"统一影视ID: {unity_id}")
    print(f"泥视频ID: {nivod_id}")
    print("-" * 80)
    
    # 1. 对比播放链接格式
    print("1️⃣ 播放链接格式对比:")
    print("-" * 60)
    compare_play_link_formats(unity_spider, nivod_spider, unity_id, nivod_id)
    
    # 2. 对比localProxy实现
    print("\n2️⃣ localProxy实现对比:")
    print("-" * 60)
    compare_localproxy_implementation(unity_spider, nivod_spider)
    
    # 3. 对比playerContent返回格式
    print("\n3️⃣ playerContent返回格式对比:")
    print("-" * 60)
    compare_playercontent_format(unity_spider, nivod_spider, unity_id, nivod_id)
    
    # 4. 分析TVBox兼容性差异
    print("\n4️⃣ TVBox兼容性差异分析:")
    print("-" * 60)
    analyze_tvbox_compatibility_differences(unity_spider, nivod_spider, unity_id, nivod_id)

def compare_play_link_formats(unity_spider, nivod_spider, unity_id, nivod_id):
    """对比播放链接格式"""
    print("播放链接格式对比:")
    
    try:
        # 获取统一影视详情
        print(f"\n统一影视播放链接格式:")
        unity_detail = unity_spider.detailContent([unity_id])
        
        if unity_detail and 'list' in unity_detail and unity_detail['list']:
            unity_video = unity_detail['list'][0]
            unity_play_url = unity_video.get('vod_play_url', '')
            
            if unity_play_url:
                unity_groups = unity_play_url.split('$$$')
                if unity_groups:
                    unity_episodes = unity_groups[0].split('#')
                    
                    print(f"  播放源数量: {len(unity_groups)}")
                    print(f"  第一播放源剧集数: {len(unity_episodes)}")
                    
                    # 显示前3集格式
                    for i, episode in enumerate(unity_episodes[:3], 1):
                        if '$' in episode:
                            ep_name, ep_url = episode.split('$', 1)
                            print(f"  第{i}集: {ep_name} -> {ep_url}")
        else:
            print("  ❌ 获取统一影视详情失败")
        
        # 获取泥视频详情
        print(f"\n泥视频播放链接格式:")
        nivod_detail = nivod_spider.detailContent([nivod_id])
        
        if nivod_detail and 'list' in nivod_detail and nivod_detail['list']:
            nivod_video = nivod_detail['list'][0]
            nivod_play_url = nivod_video.get('vod_play_url', '')
            
            if nivod_play_url:
                nivod_groups = nivod_play_url.split('$$$')
                if nivod_groups:
                    nivod_episodes = nivod_groups[0].split('#')
                    
                    print(f"  播放源数量: {len(nivod_groups)}")
                    print(f"  第一播放源剧集数: {len(nivod_episodes)}")
                    
                    # 显示前3集格式
                    for i, episode in enumerate(nivod_episodes[:3], 1):
                        if '$' in episode:
                            ep_name, ep_url = episode.split('$', 1)
                            print(f"  第{i}集: {ep_name} -> {ep_url}")
        else:
            print("  ❌ 获取泥视频详情失败")
        
        # 分析格式差异
        print(f"\n格式差异分析:")
        print(f"  统一影视: 使用标准的/index.php/vod/play/格式")
        print(f"  泥视频: 使用简化的/niplay/格式")
        print(f"  两者都符合TVBox的基本要求")
        
    except Exception as e:
        print(f"❌ 播放链接格式对比失败: {str(e)}")

def compare_localproxy_implementation(unity_spider, nivod_spider):
    """对比localProxy实现"""
    print("localProxy实现对比:")
    
    try:
        # 检查统一影视的localProxy方法
        print(f"\n统一影视localProxy:")
        if hasattr(unity_spider, 'localProxy'):
            print(f"  ✅ 有localProxy方法")
            
            # 测试代理URL生成
            test_url = "https://example.com/test.m3u8"
            try:
                # 模拟调用getProxyUrl
                if hasattr(unity_spider, 'getProxyUrl'):
                    proxy_base = unity_spider.getProxyUrl()
                    print(f"  代理基础URL: {proxy_base}")
                else:
                    print(f"  ❌ 没有getProxyUrl方法")
            except Exception as e:
                print(f"  ⚠️  getProxyUrl调用出错: {str(e)}")
        else:
            print(f"  ❌ 没有localProxy方法")
        
        # 检查泥视频的localProxy方法
        print(f"\n泥视频localProxy:")
        if hasattr(nivod_spider, 'localProxy'):
            print(f"  ✅ 有localProxy方法")
            
            # 测试代理URL生成
            test_url = "https://example.com/test.m3u8"
            try:
                # 模拟调用getProxyUrl
                if hasattr(nivod_spider, 'getProxyUrl'):
                    proxy_base = nivod_spider.getProxyUrl()
                    print(f"  代理基础URL: {proxy_base}")
                    
                    # 测试_get_proxy_url方法
                    if hasattr(nivod_spider, '_get_proxy_url'):
                        proxy_url = nivod_spider._get_proxy_url(test_url, 'm3u8')
                        print(f"  代理URL示例: {proxy_url}")
                    else:
                        print(f"  ❌ 没有_get_proxy_url方法")
                else:
                    print(f"  ❌ 没有getProxyUrl方法")
            except Exception as e:
                print(f"  ⚠️  代理URL生成出错: {str(e)}")
        else:
            print(f"  ❌ 没有localProxy方法")
        
        # 分析实现差异
        print(f"\nlocalProxy实现差异:")
        print(f"  统一影视: 可能使用框架默认的localProxy实现")
        print(f"  泥视频: 自定义实现了localProxy和_get_proxy_url方法")
        print(f"  两者都应该能够处理M3U8流媒体代理")
        
    except Exception as e:
        print(f"❌ localProxy实现对比失败: {str(e)}")

def compare_playercontent_format(unity_spider, nivod_spider, unity_id, nivod_id):
    """对比playerContent返回格式"""
    print("playerContent返回格式对比:")
    
    try:
        # 测试统一影视的playerContent
        print(f"\n统一影视playerContent:")
        unity_test_id = f"{unity_id}-3-1"  # 第一播放源第一集
        
        try:
            unity_result = unity_spider.playerContent("", unity_test_id, "")
            
            if unity_result:
                print(f"  ✅ 播放链接获取成功")
                print(f"  URL: {unity_result.get('url', '无')[:80]}{'...' if len(str(unity_result.get('url', ''))) > 80 else ''}")
                print(f"  Parse: {unity_result.get('parse', '无')}")
                print(f"  Header: {'有' if unity_result.get('header') else '无'}")
                
                # 检查URL类型
                url = unity_result.get('url', '')
                if url:
                    if '127.0.0.1:9978' in url:
                        print(f"  🔗 代理链接类型")
                    elif url.startswith('http'):
                        print(f"  🔗 直接链接类型")
                    else:
                        print(f"  🔗 其他链接类型")
            else:
                print(f"  ❌ 播放链接获取失败")
        except Exception as e:
            print(f"  ❌ playerContent调用出错: {str(e)}")
        
        # 测试泥视频的playerContent
        print(f"\n泥视频playerContent:")
        nivod_test_id = f"{nivod_id}-1-1"  # 第一播放源第一集
        
        try:
            nivod_result = nivod_spider.playerContent("", nivod_test_id, "")
            
            if nivod_result:
                print(f"  ✅ 播放链接获取成功")
                print(f"  URL: {nivod_result.get('url', '无')[:80]}{'...' if len(str(nivod_result.get('url', ''))) > 80 else ''}")
                print(f"  Parse: {nivod_result.get('parse', '无')}")
                print(f"  Header: {'有' if nivod_result.get('header') else '无'}")
                
                # 检查URL类型
                url = nivod_result.get('url', '')
                if url:
                    if '127.0.0.1:9978' in url:
                        print(f"  🔗 代理链接类型")
                        
                        # 解析代理链接
                        if '/m3u8?url=' in url:
                            encoded_url = url.split('/m3u8?url=')[1]
                            original_url = urllib.parse.unquote(encoded_url)
                            print(f"  原始链接: {original_url[:60]}{'...' if len(original_url) > 60 else ''}")
                    elif url.startswith('http'):
                        print(f"  🔗 直接链接类型")
                    else:
                        print(f"  🔗 其他链接类型")
            else:
                print(f"  ❌ 播放链接获取失败")
        except Exception as e:
            print(f"  ❌ playerContent调用出错: {str(e)}")
        
        # 分析返回格式差异
        print(f"\nplayerContent格式差异:")
        print(f"  两者都返回标准的播放结果字典")
        print(f"  包含url、parse、header等必需字段")
        print(f"  泥视频使用localProxy代理，统一影视可能直接返回")
        
    except Exception as e:
        print(f"❌ playerContent格式对比失败: {str(e)}")

def analyze_tvbox_compatibility_differences(unity_spider, nivod_spider, unity_id, nivod_id):
    """分析TVBox兼容性差异"""
    print("TVBox兼容性差异分析:")
    
    try:
        # 获取两个爬虫的完整数据
        unity_detail = unity_spider.detailContent([unity_id])
        nivod_detail = nivod_spider.detailContent([nivod_id])
        
        if not unity_detail or not nivod_detail:
            print("❌ 无法获取对比数据")
            return
        
        unity_video = unity_detail['list'][0] if unity_detail.get('list') else {}
        nivod_video = nivod_detail['list'][0] if nivod_detail.get('list') else {}
        
        print(f"\n数据完整性对比:")
        
        # 对比关键字段
        key_fields = ['vod_id', 'vod_name', 'vod_pic', 'vod_content', 'vod_play_from', 'vod_play_url']
        
        for field in key_fields:
            unity_value = unity_video.get(field, '')
            nivod_value = nivod_video.get(field, '')
            
            unity_status = "✅" if unity_value else "❌"
            nivod_status = "✅" if nivod_value else "❌"
            
            print(f"  {field}:")
            print(f"    统一影视: {unity_status}")
            print(f"    泥视频: {nivod_status}")
        
        # 分析可能的兼容性问题
        print(f"\n可能的兼容性问题分析:")
        
        issues = []
        
        # 检查vod_name字段
        if not unity_video.get('vod_name', ''):
            issues.append("统一影视缺少vod_name字段，可能影响TVBox显示")
        
        # 检查播放源格式
        unity_play_from = unity_video.get('vod_play_from', '')
        nivod_play_from = nivod_video.get('vod_play_from', '')
        
        if '[SID:' in unity_play_from and '[SID:' not in nivod_play_from:
            issues.append("泥视频缺少SID标识，可能影响TVBox播放源识别")
        
        # 检查URL格式
        unity_play_url = unity_video.get('vod_play_url', '')
        nivod_play_url = nivod_video.get('vod_play_url', '')
        
        if '/index.php/vod/play/' in unity_play_url and '/niplay/' in nivod_play_url:
            issues.append("URL格式不同，可能影响TVBox的URL解析")
        
        # 检查代理使用
        # 测试播放链接
        try:
            unity_test_result = unity_spider.playerContent("", f"{unity_id}-3-1", "")
            nivod_test_result = nivod_spider.playerContent("", f"{nivod_id}-1-1", "")
            
            unity_url = unity_test_result.get('url', '') if unity_test_result else ''
            nivod_url = nivod_test_result.get('url', '') if nivod_test_result else ''
            
            unity_is_proxy = '127.0.0.1:9978' in unity_url
            nivod_is_proxy = '127.0.0.1:9978' in nivod_url
            
            if unity_is_proxy != nivod_is_proxy:
                issues.append("代理使用方式不同，可能影响TVBox播放")
        except:
            pass
        
        if issues:
            print(f"发现的潜在问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print(f"✅ 未发现明显的兼容性问题")
        
        # 提供修复建议
        print(f"\n修复建议:")
        print(f"1. 确保泥视频的vod_name字段有意义的值")
        print(f"2. 考虑在播放源名称中添加SID标识")
        print(f"3. 验证localProxy代理服务是否正常运行")
        print(f"4. 测试在实际TVBox环境中的表现")
        print(f"5. 检查TVBox应用的版本和配置")
        
    except Exception as e:
        print(f"❌ TVBox兼容性差异分析失败: {str(e)}")

if __name__ == '__main__':
    compare_tvbox_playback()
