# -*- coding: utf-8 -*-
"""
测试修复后的播放功能，验证是否能正确提取真实的视频直链
"""

import sys
import requests
import json
import re

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_fixed_play_function():
    """测试修复后的播放功能"""
    print("=" * 60)
    print("测试修复后的播放功能")
    print("=" * 60)
    
    try:
        from plugin.html.泥视频 import Spider
        spider = Spider()
        spider.init()
        
        # 1. 获取测试视频详情
        print("\n1. 获取测试视频详情")
        print("-" * 40)
        
        test_vod_id = "84898"
        detail_result = spider.detailContent([test_vod_id])
        
        if detail_result and detail_result.get('list'):
            vod_info = detail_result['list'][0]
            print(f"✓ 详情获取成功")
            print(f"  视频ID: {vod_info.get('vod_id', '')}")
            print(f"  视频标题: {vod_info.get('vod_name', '')}")
            
            # 获取播放源信息
            play_from = vod_info.get('vod_play_from', '')
            play_url = vod_info.get('vod_play_url', '')
            
            if play_from and play_url:
                print(f"  播放源: {play_from}")
                
                # 解析播放链接
                from_list = play_from.split('$$$')
                url_list = play_url.split('$$$')
                
                if url_list and url_list[0]:
                    episodes = url_list[0].split('#')
                    if episodes and episodes[0]:
                        first_episode = episodes[0]
                        if '$' in first_episode:
                            ep_name, ep_id = first_episode.split('$', 1)
                            
                            print(f"\n2. 测试修复后的播放链接获取")
                            print("-" * 40)
                            print(f"测试播放: {ep_name} (ID: {ep_id})")
                            
                            # 调用修复后的 playerContent
                            flag = from_list[0] if from_list else "默认播放源"
                            player_result = spider.playerContent(flag, ep_id, [])
                            
                            print(f"\n播放结果:")
                            print(f"  parse: {player_result.get('parse', 'N/A')}")
                            play_url_result = player_result.get('url', 'N/A')
                            print(f"  url: {play_url_result}")
                            print(f"  header: {player_result.get('header', 'N/A')}")
                            
                            # 3. 验证播放链接类型
                            print(f"\n3. 播放链接类型验证")
                            print("-" * 40)
                            
                            if play_url_result and play_url_result != 'N/A':
                                # 检查是否为真实视频链接
                                if any(ext in play_url_result.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                                    print(f"✓ 这是真实的视频直链")
                                    
                                    # 检查是否使用了代理
                                    if '127.0.0.1:9978' in play_url_result:
                                        print(f"✓ 使用了localProxy代理")
                                        
                                        # 解析代理URL中的真实链接
                                        if 'url=' in play_url_result:
                                            try:
                                                from base64 import b64decode
                                                from urllib.parse import parse_qs, urlparse
                                                
                                                parsed = urlparse(play_url_result)
                                                params = parse_qs(parsed.query)
                                                
                                                if 'url' in params:
                                                    encoded_url = params['url'][0]
                                                    decoded_url = b64decode(encoded_url).decode('utf-8')
                                                    print(f"  代理的真实链接: {decoded_url}")
                                                    
                                                    # 验证真实链接的可访问性
                                                    print(f"\n4. 真实链接可访问性验证")
                                                    print("-" * 40)
                                                    
                                                    try:
                                                        response = requests.head(decoded_url, timeout=10, allow_redirects=True)
                                                        if response.status_code == 200:
                                                            print(f"✓ 真实链接可访问 (状态码: {response.status_code})")
                                                            content_type = response.headers.get('Content-Type', '')
                                                            print(f"  内容类型: {content_type}")
                                                            
                                                            if 'mpegurl' in content_type or 'application/vnd.apple.mpegurl' in content_type:
                                                                print(f"✓ 确认为m3u8流媒体文件")
                                                            elif 'video' in content_type:
                                                                print(f"✓ 确认为视频文件")
                                                            else:
                                                                print(f"⚠️ 内容类型未知")
                                                        else:
                                                            print(f"⚠️ 真实链接状态异常 (状态码: {response.status_code})")
                                                    except Exception as e:
                                                        print(f"⚠️ 真实链接访问测试失败: {str(e)}")
                                                        
                                            except Exception as e:
                                                print(f"✗ 代理URL解析失败: {str(e)}")
                                    else:
                                        print(f"⚠️ 未使用代理，直接返回视频链接")
                                        
                                        # 验证直链的可访问性
                                        print(f"\n4. 直链可访问性验证")
                                        print("-" * 40)
                                        
                                        try:
                                            response = requests.head(play_url_result, timeout=10, allow_redirects=True)
                                            if response.status_code == 200:
                                                print(f"✓ 直链可访问 (状态码: {response.status_code})")
                                                content_type = response.headers.get('Content-Type', '')
                                                print(f"  内容类型: {content_type}")
                                            else:
                                                print(f"⚠️ 直链状态异常 (状态码: {response.status_code})")
                                        except Exception as e:
                                            print(f"⚠️ 直链访问测试失败: {str(e)}")
                                            
                                elif 'nivod.vip' in play_url_result and '/niplay/' in play_url_result:
                                    print(f"✗ 这是播放页面URL，不是视频直链")
                                    print(f"  问题: 视频链接提取失败，返回了播放页面URL")
                                    print(f"  建议: 检查_extract_play_url()方法的实现")
                                else:
                                    print(f"⚠️ 链接类型未知")
                                    print(f"  链接: {play_url_result}")
                            else:
                                print(f"✗ 播放链接为空")
                            
                            # 5. 测试localProxy功能
                            print(f"\n5. 测试localProxy功能")
                            print("-" * 40)
                            
                            # 模拟代理参数
                            from base64 import b64encode
                            test_m3u8_url = "https://hn.bfvvs.com/play/mbkzB4Xa/index.m3u8"
                            encoded_url = b64encode(test_m3u8_url.encode('utf-8')).decode('utf-8')
                            
                            proxy_param = {
                                'url': encoded_url,
                                'type': 'm3u8'
                            }
                            
                            try:
                                proxy_result = spider.localProxy(proxy_param)
                                if proxy_result:
                                    print(f"✓ localProxy方法工作正常")
                                    if isinstance(proxy_result, list) and len(proxy_result) >= 3:
                                        print(f"  状态码: {proxy_result[0]}")
                                        print(f"  内容类型: {proxy_result[1]}")
                                        print(f"  内容长度: {len(proxy_result[2]) if proxy_result[2] else 0}")
                                    else:
                                        print(f"  返回内容: {proxy_result}")
                                else:
                                    print(f"⚠️ localProxy方法返回空结果")
                            except Exception as e:
                                print(f"✗ localProxy方法测试失败: {str(e)}")
                        else:
                            print(f"✗ 播放链接格式错误: {first_episode}")
                    else:
                        print(f"✗ 没有找到播放集数")
                else:
                    print(f"✗ 没有找到播放链接")
            else:
                print(f"✗ 播放源或播放链接为空")
        else:
            print(f"✗ 详情获取失败")
            
        # 6. 总结修复效果
        print(f"\n" + "=" * 60)
        print("修复效果总结")
        print("=" * 60)
        
        print(f"✓ 修复内容:")
        print(f"  - 改进了_extract_play_url()方法")
        print(f"  - 优先解析JavaScript变量（player_aaaa等）")
        print(f"  - 使用改进的正则表达式模式")
        print(f"  - 正确处理JSON转义字符")
        print(f"  - 验证视频链接的有效性")
        
        print(f"\n✓ 预期效果:")
        print(f"  - 返回真实的视频直链而不是播放页面URL")
        print(f"  - 支持m3u8、mp4、flv等视频格式")
        print(f"  - 通过localProxy代理解决跨域问题")
        print(f"  - 兼容TVBox等播放器")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_play_function()
