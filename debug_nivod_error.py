# -*- coding: utf-8 -*-
"""
调试泥视频爬虫的错误
"""

import sys

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def debug_nivod_error():
    """调试泥视频爬虫错误"""
    print("=" * 80)
    print("🐛 调试泥视频爬虫错误")
    print("=" * 80)
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider
        spider = Spider()
        spider.init()
        print("✅ 泥视频爬虫初始化成功")
    except Exception as e:
        print(f"❌ 泥视频爬虫初始化失败: {str(e)}")
        return
    
    # 测试_extract_play_sources方法
    test_id = "82135"
    
    try:
        # 获取详情页面HTML
        detail_url = f"{spider.host}/nivod/{test_id}/"
        print(f"详情页面URL: {detail_url}")
        
        rsp = spider.fetch(detail_url, headers=spider.headers)
        if rsp and rsp.status_code == 200:
            doc = spider.html(rsp.text)
            
            # 直接调用_extract_play_sources方法
            print("调用_extract_play_sources方法...")
            play_from, play_url = spider._extract_play_sources(doc, test_id)
            
            print(f"返回结果:")
            print(f"  play_from类型: {type(play_from)}")
            print(f"  play_from内容: {play_from}")
            print(f"  play_url类型: {type(play_url)}")
            print(f"  play_url内容: {play_url}")
            
            if isinstance(play_url, list):
                print("✅ play_url是列表类型")
                for i, url in enumerate(play_url):
                    print(f"    {i}: {url} (类型: {type(url)})")
            else:
                print(f"❌ play_url不是列表类型: {type(play_url)}")
                
        else:
            print(f"❌ 详情页面请求失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_nivod_error()
