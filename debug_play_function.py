# -*- coding: utf-8 -*-
"""
调试播放功能问题
"""

import sys
import requests
from lxml import etree
import json

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def debug_play_function():
    """调试播放功能"""
    print("=" * 60)
    print("调试泥视频播放功能问题")
    print("=" * 60)
    
    try:
        from plugin.html.泥视频 import Spider
        spider = Spider()
        spider.init()
        
        # 1. 测试详情页面获取
        print("\n1. 测试详情页面获取")
        print("-" * 40)
        
        test_vod_id = "84898"  # 使用之前测试过的视频ID
        detail_result = spider.detailContent([test_vod_id])
        
        if detail_result and detail_result.get('list'):
            vod_info = detail_result['list'][0]
            print(f"✓ 详情获取成功")
            print(f"  视频ID: {vod_info.get('vod_id', '')}")
            print(f"  视频标题: {vod_info.get('vod_name', '')}")
            print(f"  播放源: {vod_info.get('vod_play_from', '')}")
            print(f"  播放链接: {vod_info.get('vod_play_url', '')}")
            
            # 分析播放源格式
            play_from = vod_info.get('vod_play_from', '')
            play_url = vod_info.get('vod_play_url', '')
            
            if play_from and play_url:
                print(f"\n播放源分析:")
                from_list = play_from.split('$$$')
                url_list = play_url.split('$$$')
                
                for i, (source_name, source_urls) in enumerate(zip(from_list, url_list)):
                    print(f"  播放源 {i+1}: {source_name}")
                    episodes = source_urls.split('#')
                    print(f"    集数: {len(episodes)}")
                    for j, episode in enumerate(episodes[:3]):  # 只显示前3集
                        if '$' in episode:
                            ep_name, ep_id = episode.split('$', 1)
                            print(f"      第{j+1}集: {ep_name} -> {ep_id}")
                        else:
                            print(f"      第{j+1}集: {episode}")
                
                # 2. 测试播放链接获取
                print(f"\n2. 测试播放链接获取")
                print("-" * 40)
                
                # 获取第一个播放源的第一集
                if url_list and url_list[0]:
                    first_source_episodes = url_list[0].split('#')
                    if first_source_episodes and first_source_episodes[0]:
                        first_episode = first_source_episodes[0]
                        if '$' in first_episode:
                            ep_name, ep_id = first_episode.split('$', 1)
                            print(f"测试播放: {ep_name} (ID: {ep_id})")
                            
                            # 调用 playerContent
                            flag = from_list[0] if from_list else "默认播放源"
                            player_result = spider.playerContent(flag, ep_id, [])
                            
                            print(f"播放结果:")
                            print(f"  parse: {player_result.get('parse', 'N/A')}")
                            print(f"  url: {player_result.get('url', 'N/A')}")
                            print(f"  header: {player_result.get('header', 'N/A')}")
                            
                            # 3. 分析播放页面内容
                            if player_result.get('url'):
                                print(f"\n3. 分析播放页面内容")
                                print("-" * 40)
                                
                                play_url = player_result['url']
                                print(f"播放页面URL: {play_url}")
                                
                                # 直接访问播放页面
                                try:
                                    response = requests.get(play_url, headers=spider.headers, timeout=10)
                                    if response.status_code == 200:
                                        print(f"✓ 播放页面访问成功 (长度: {len(response.text)})")
                                        
                                        # 查找可能的视频链接
                                        content = response.text
                                        
                                        # 查找常见的视频链接模式
                                        import re
                                        video_patterns = [
                                            r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
                                            r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
                                            r'video\s*:\s*"([^"]+)"',
                                            r'source\s*:\s*"([^"]+)"',
                                            r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
                                        ]
                                        
                                        found_videos = []
                                        for pattern in video_patterns:
                                            matches = re.findall(pattern, content, re.IGNORECASE)
                                            for match in matches:
                                                if match and ('http' in match or match.startswith('//')):
                                                    found_videos.append(match)
                                        
                                        if found_videos:
                                            print(f"✓ 找到 {len(found_videos)} 个可能的视频链接:")
                                            for i, video_url in enumerate(found_videos[:5]):
                                                print(f"  视频链接 {i+1}: {video_url}")
                                        else:
                                            print(f"⚠️ 未找到直接的视频链接")
                                            
                                            # 查找JavaScript代码
                                            js_patterns = [
                                                r'<script[^>]*>(.*?)</script>',
                                                r'var\s+\w+\s*=\s*["\'][^"\']*["\']',
                                                r'player\s*\([^)]*\)',
                                            ]
                                            
                                            print(f"查找JavaScript代码:")
                                            for pattern in js_patterns:
                                                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                                                if matches:
                                                    print(f"  找到 {len(matches)} 个匹配的JS代码段")
                                                    for j, match in enumerate(matches[:2]):
                                                        preview = match[:200] + "..." if len(match) > 200 else match
                                                        print(f"    JS {j+1}: {preview}")
                                    else:
                                        print(f"✗ 播放页面访问失败: {response.status_code}")
                                except Exception as e:
                                    print(f"✗ 播放页面访问出错: {str(e)}")
                        else:
                            print(f"✗ 播放链接格式错误: {first_episode}")
                    else:
                        print(f"✗ 没有找到播放集数")
                else:
                    print(f"✗ 没有找到播放链接")
            else:
                print(f"✗ 播放源或播放链接为空")
                print(f"  play_from: {play_from}")
                print(f"  play_url: {play_url}")
        else:
            print(f"✗ 详情获取失败")
            
        # 4. 对比其他成功爬虫的格式
        print(f"\n4. 标准播放格式参考")
        print("-" * 40)
        print(f"标准格式示例:")
        print(f"  vod_play_from: '播放源1$$$播放源2'")
        print(f"  vod_play_url: '第1集$id1#第2集$id2$$$第1集$id1#第2集$id2'")
        print(f"  playerContent返回: {{'parse': 0, 'url': '直链', 'header': {{}}}}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_play_function()
