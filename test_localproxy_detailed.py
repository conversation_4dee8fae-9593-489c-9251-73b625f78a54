# -*- coding: utf-8 -*-
"""
详细测试泥视频爬虫的localProxy方法实际工作情况
"""

import sys
import requests
from base64 import b64encode, b64decode
import urllib.parse

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_localproxy_detailed():
    """详细测试localProxy方法"""
    print("=" * 80)
    print("🔬 泥视频localProxy方法详细测试")
    print("=" * 80)
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider
        spider = Spider()
        spider.init()
        print("✅ 泥视频爬虫初始化成功")
    except Exception as e:
        print(f"❌ 泥视频爬虫初始化失败: {str(e)}")
        return
    
    # 1. 测试真实的播放链接获取
    print("\n1. 测试播放链接获取")
    print("-" * 50)
    
    test_id = "84898-1-1"  # 使用之前测试成功的ID
    try:
        play_result = spider.playerContent("自营1线", test_id, [])
        print(f"播放结果: {play_result}")
        
        if play_result and 'url' in play_result:
            proxy_url = play_result['url']
            print(f"代理URL: {proxy_url}")
            
            # 解析代理URL参数
            if '?do=py&url=' in proxy_url:
                url_part = proxy_url.split('?do=py&url=')[1]
                if '&type=' in url_part:
                    encoded_url = url_part.split('&type=')[0]
                    media_type = url_part.split('&type=')[1]
                    
                    print(f"编码的URL: {encoded_url}")
                    print(f"媒体类型: {media_type}")
                    
                    # 解码URL
                    try:
                        decoded_url = b64decode(encoded_url).decode('utf-8')
                        print(f"解码后的URL: {decoded_url}")
                        
                        # 验证解码后的URL是否为真实视频链接
                        if any(ext in decoded_url.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                            print("✅ 解码后的URL是真实视频链接")
                            
                            # 测试直接访问解码后的URL
                            test_direct_access(decoded_url)
                        else:
                            print("❌ 解码后的URL不是视频链接")
                    except Exception as e:
                        print(f"❌ URL解码失败: {str(e)}")
        else:
            print("❌ 未获取到播放链接")
            
    except Exception as e:
        print(f"❌ 播放链接获取失败: {str(e)}")
    
    # 2. 直接测试localProxy方法
    print("\n2. 直接测试localProxy方法")
    print("-" * 50)
    
    # 使用已知的真实视频URL
    test_video_url = "https://hn.bfvvs.com/play/mbkzB4Xa/index.m3u8"
    encoded_url = b64encode(test_video_url.encode('utf-8')).decode('utf-8')
    
    proxy_param = {
        'url': encoded_url,
        'type': 'm3u8'
    }
    
    print(f"测试参数: {proxy_param}")
    
    try:
        proxy_result = spider.localProxy(proxy_param)
        print(f"localProxy返回结果类型: {type(proxy_result)}")
        print(f"localProxy返回结果长度: {len(proxy_result) if proxy_result else 0}")
        
        if proxy_result and len(proxy_result) >= 3:
            status_code = proxy_result[0]
            content_type = proxy_result[1]
            content = proxy_result[2]
            
            print(f"状态码: {status_code}")
            print(f"内容类型: {content_type}")
            print(f"内容长度: {len(content) if content else 0}")
            
            if status_code == 200 and content:
                print("✅ localProxy成功处理了m3u8文件")
                
                # 分析m3u8内容
                if isinstance(content, bytes):
                    content_str = content.decode('utf-8')
                else:
                    content_str = str(content)
                
                lines = content_str.split('\n')[:10]  # 只显示前10行
                print("m3u8内容预览:")
                for i, line in enumerate(lines):
                    print(f"  {i+1}: {line}")
                
                # 检查是否包含代理URL
                proxy_count = sum(1 for line in content_str.split('\n') if 'http://127.0.0.1:9978' in line)
                print(f"包含代理URL的行数: {proxy_count}")
                
                if proxy_count > 0:
                    print("✅ m3u8文件中的TS片段已正确代理")
                else:
                    print("❌ m3u8文件中的TS片段未被代理")
            else:
                print("❌ localProxy处理失败")
        else:
            print("❌ localProxy返回格式错误")
            
    except Exception as e:
        print(f"❌ localProxy测试失败: {str(e)}")
    
    # 3. 对比不同编码方式的效果
    print("\n3. 对比不同编码方式")
    print("-" * 50)
    
    # Base64编码（当前方式）
    base64_encoded = b64encode(test_video_url.encode('utf-8')).decode('utf-8')
    base64_proxy_url = f"http://127.0.0.1:9978?do=py&url={base64_encoded}&type=m3u8"
    
    # URL编码（统一影视方式）
    url_encoded = urllib.parse.quote(test_video_url, safe='')
    url_proxy_url = f"http://127.0.0.1:9978/m3u8?url={url_encoded}"
    
    print("Base64编码方式:")
    print(f"  代理URL长度: {len(base64_proxy_url)}")
    print(f"  代理URL: {base64_proxy_url}")
    
    print("\nURL编码方式:")
    print(f"  代理URL长度: {len(url_proxy_url)}")
    print(f"  代理URL: {url_proxy_url}")
    
    # 4. 分析问题和建议
    print("\n4. 问题分析和建议")
    print("-" * 50)
    
    print("🔍 当前状态分析:")
    print("1. 泥视频能够正确提取真实的视频直链")
    print("2. localProxy方法能够正确解码base64编码的URL")
    print("3. localProxy能够处理m3u8文件并为TS片段生成代理URL")
    
    print("\n💡 可能的问题点:")
    print("1. 代理URL格式可能与某些播放器不兼容")
    print("2. m3u8文件处理可能存在细节问题")
    print("3. 播放器可能需要特定的HTTP头信息")
    
    print("\n🛠️ 建议的改进方向:")
    print("1. 验证代理URL是否符合TVBox等播放器的要求")
    print("2. 检查m3u8文件的Content-Type是否正确")
    print("3. 考虑添加更多的HTTP头信息支持")
    print("4. 测试不同播放器的兼容性")

def test_direct_access(url):
    """测试直接访问URL"""
    print(f"\n🔗 测试直接访问: {url}")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.nivod.vip/'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"内容类型: {response.headers.get('Content-Type', 'Unknown')}")
        print(f"内容长度: {len(response.content)}")
        
        if response.status_code == 200:
            print("✅ URL可直接访问")
            
            # 如果是m3u8文件，显示前几行内容
            if '.m3u8' in url:
                content = response.text
                lines = content.split('\n')[:5]
                print("m3u8内容预览:")
                for i, line in enumerate(lines):
                    print(f"  {i+1}: {line}")
        else:
            print("❌ URL无法直接访问")
            
    except Exception as e:
        print(f"❌ 直接访问测试失败: {str(e)}")

if __name__ == '__main__':
    test_localproxy_detailed()
