# -*- coding: utf-8 -*-
"""
分析泥视频播放页面结构，找出真实的视频链接提取方法
"""

import sys
import requests
import json
import re
from urllib.parse import urljoin

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def analyze_play_page():
    """分析播放页面结构"""
    print("=" * 60)
    print("分析泥视频播放页面结构")
    print("=" * 60)
    
    try:
        from plugin.html.泥视频 import Spider
        spider = Spider()
        spider.init()
        
        # 1. 获取一个测试视频的详情
        print("\n1. 获取测试视频详情")
        print("-" * 40)
        
        test_vod_id = "84898"
        detail_result = spider.detailContent([test_vod_id])
        
        if detail_result and detail_result.get('list'):
            vod_info = detail_result['list'][0]
            play_url = vod_info.get('vod_play_url', '')
            
            if play_url:
                # 解析播放链接
                url_list = play_url.split('$$$')
                if url_list and url_list[0]:
                    episodes = url_list[0].split('#')
                    if episodes and episodes[0]:
                        first_episode = episodes[0]
                        if '$' in first_episode:
                            ep_name, ep_id = first_episode.split('$', 1)
                            
                            # 构建播放页面URL
                            parts = ep_id.split('-')
                            if len(parts) >= 3:
                                vod_id, source, episode = parts[0], parts[1], parts[2]
                                play_page_url = f"{spider.host}/niplay/{vod_id}-{source}-{episode}/"
                                
                                print(f"播放页面URL: {play_page_url}")
                                
                                # 2. 访问播放页面并分析结构
                                print(f"\n2. 分析播放页面结构")
                                print("-" * 40)
                                
                                response = requests.get(play_page_url, headers=spider.headers, timeout=15)
                                if response.status_code == 200:
                                    content = response.text
                                    print(f"页面内容长度: {len(content)}")
                                    
                                    # 3. 查找JavaScript中的视频配置
                                    print(f"\n3. 查找JavaScript中的视频配置")
                                    print("-" * 40)
                                    
                                    # 查找script标签中的配置
                                    script_patterns = [
                                        r'<script[^>]*>(.*?)</script>',
                                        r'var\s+player\s*=\s*({.*?});',
                                        r'var\s+config\s*=\s*({.*?});',
                                        r'player\s*=\s*({.*?});',
                                        r'config\s*=\s*({.*?});'
                                    ]
                                    
                                    found_configs = []
                                    for pattern in script_patterns:
                                        matches = re.findall(pattern, content, re.DOTALL | re.IGNORECASE)
                                        for match in matches:
                                            if 'url' in match.lower() or 'video' in match.lower():
                                                found_configs.append(match[:200] + "..." if len(match) > 200 else match)
                                    
                                    if found_configs:
                                        print(f"找到 {len(found_configs)} 个可能的配置:")
                                        for i, config in enumerate(found_configs[:3]):  # 只显示前3个
                                            print(f"  配置 {i+1}: {config}")
                                    else:
                                        print("未找到明显的JavaScript配置")
                                    
                                    # 4. 查找可能的API调用
                                    print(f"\n4. 查找可能的API调用")
                                    print("-" * 40)
                                    
                                    api_patterns = [
                                        r'fetch\s*\(\s*["\']([^"\']+)["\']',
                                        r'ajax\s*\(\s*["\']([^"\']+)["\']',
                                        r'\.get\s*\(\s*["\']([^"\']+)["\']',
                                        r'\.post\s*\(\s*["\']([^"\']+)["\']',
                                        r'url\s*:\s*["\']([^"\']*api[^"\']*)["\']',
                                        r'["\']([^"\']*api[^"\']*\.php[^"\']*)["\']'
                                    ]
                                    
                                    found_apis = []
                                    for pattern in api_patterns:
                                        matches = re.findall(pattern, content, re.IGNORECASE)
                                        for match in matches:
                                            if match and ('api' in match.lower() or 'play' in match.lower()):
                                                found_apis.append(match)
                                    
                                    if found_apis:
                                        print(f"找到 {len(found_apis)} 个可能的API:")
                                        for api in set(found_apis[:5]):  # 去重并只显示前5个
                                            print(f"  API: {api}")
                                    else:
                                        print("未找到明显的API调用")
                                    
                                    # 5. 查找iframe播放器
                                    print(f"\n5. 查找iframe播放器")
                                    print("-" * 40)
                                    
                                    iframe_patterns = [
                                        r'<iframe[^>]+src\s*=\s*["\']([^"\']+)["\']',
                                        r'iframe\s*=\s*["\']([^"\']+)["\']'
                                    ]
                                    
                                    found_iframes = []
                                    for pattern in iframe_patterns:
                                        matches = re.findall(pattern, content, re.IGNORECASE)
                                        for match in matches:
                                            if match and ('play' in match.lower() or 'video' in match.lower()):
                                                found_iframes.append(match)
                                    
                                    if found_iframes:
                                        print(f"找到 {len(found_iframes)} 个iframe:")
                                        for iframe in found_iframes:
                                            print(f"  iframe: {iframe}")
                                            
                                            # 尝试访问iframe内容
                                            try:
                                                if not iframe.startswith('http'):
                                                    iframe = urljoin(spider.host, iframe)
                                                
                                                iframe_response = requests.get(iframe, headers=spider.headers, timeout=10)
                                                if iframe_response.status_code == 200:
                                                    iframe_content = iframe_response.text
                                                    print(f"    iframe内容长度: {len(iframe_content)}")
                                                    
                                                    # 在iframe中查找视频链接
                                                    video_patterns = [
                                                        r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
                                                        r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
                                                        r'video\s*:\s*"([^"]+)"',
                                                        r'source\s*:\s*"([^"]+)"',
                                                        r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
                                                    ]
                                                    
                                                    for pattern in video_patterns:
                                                        matches = re.findall(pattern, iframe_content, re.IGNORECASE)
                                                        for match in matches:
                                                            if match and ('http' in match or match.startswith('//')):
                                                                print(f"    找到视频链接: {match}")
                                                                
                                            except Exception as e:
                                                print(f"    访问iframe失败: {str(e)}")
                                    else:
                                        print("未找到iframe播放器")
                                    
                                    # 6. 直接查找视频链接
                                    print(f"\n6. 直接查找视频链接")
                                    print("-" * 40)
                                    
                                    video_patterns = [
                                        r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
                                        r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
                                        r'video\s*:\s*"([^"]+)"',
                                        r'source\s*:\s*"([^"]+)"',
                                        r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
                                    ]
                                    
                                    found_videos = []
                                    for pattern in video_patterns:
                                        matches = re.findall(pattern, content, re.IGNORECASE)
                                        for match in matches:
                                            if match and ('http' in match or match.startswith('//')):
                                                found_videos.append(match)
                                    
                                    if found_videos:
                                        print(f"找到 {len(found_videos)} 个视频链接:")
                                        for video in set(found_videos):  # 去重
                                            print(f"  视频: {video}")
                                            
                                            # 验证视频链接可访问性
                                            try:
                                                clean_url = video.replace('\\/', '/')
                                                test_response = requests.head(clean_url, timeout=5)
                                                print(f"    状态: {test_response.status_code}")
                                            except Exception as e:
                                                print(f"    访问失败: {str(e)}")
                                    else:
                                        print("未找到直接的视频链接")
                                    
                                    # 7. 保存页面内容用于进一步分析
                                    print(f"\n7. 保存页面内容")
                                    print("-" * 40)
                                    
                                    with open('play_page_content.html', 'w', encoding='utf-8') as f:
                                        f.write(content)
                                    print(f"页面内容已保存到 play_page_content.html")
                                    
                                else:
                                    print(f"播放页面访问失败: {response.status_code}")
                            else:
                                print("播放ID格式错误")
                        else:
                            print("播放链接格式错误")
                    else:
                        print("没有找到播放集数")
                else:
                    print("没有找到播放链接")
            else:
                print("播放源为空")
        else:
            print("详情获取失败")
            
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_play_page()
