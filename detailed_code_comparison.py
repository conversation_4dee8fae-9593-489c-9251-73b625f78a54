# -*- coding: utf-8 -*-
"""
详细的代码对比分析 - 找出TVBox跳转问题的根本原因
"""

import sys
import re
import json
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def detailed_code_comparison():
    """详细的代码对比分析"""
    print("=" * 120)
    print("🔍 详细代码对比分析 - TVBox跳转问题诊断")
    print("=" * 120)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入爬虫
    try:
        from 统一影视 import Spider as UnitySpider
        from 泥视频 import Spider as NivodSpider
        
        unity_spider = UnitySpider()
        unity_spider.init()
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        print("✅ 爬虫初始化成功")
        print(f"统一影视: {unity_spider.host}")
        print(f"泥视频: {nivod_spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频ID
    unity_id = "73268"  # 斗罗大陆II绝世唐门
    nivod_id = "82135"  # 斗罗大陆II绝世唐门
    
    print(f"\n📺 测试视频:")
    print(f"统一影视ID: {unity_id}")
    print(f"泥视频ID: {nivod_id}")
    print("-" * 80)
    
    # 1. detailContent方法对比
    print("1️⃣ detailContent方法对比分析:")
    print("-" * 60)
    compare_detail_content_methods(unity_spider, nivod_spider, unity_id, nivod_id)
    
    # 2. playerContent方法对比
    print("\n2️⃣ playerContent方法对比分析:")
    print("-" * 60)
    compare_player_content_methods(unity_spider, nivod_spider, unity_id, nivod_id)
    
    # 3. TVBox跳转机制分析
    print("\n3️⃣ TVBox跳转机制分析:")
    print("-" * 60)
    analyze_tvbox_jump_mechanism(unity_spider, nivod_spider, unity_id, nivod_id)
    
    # 4. 具体问题定位
    print("\n4️⃣ 具体问题定位:")
    print("-" * 60)
    identify_specific_issues(unity_spider, nivod_spider, unity_id, nivod_id)

def compare_detail_content_methods(unity_spider, nivod_spider, unity_id, nivod_id):
    """对比detailContent方法"""
    print("detailContent方法实现对比:")
    
    try:
        # 获取统一影视详情
        print(f"\n统一影视detailContent分析:")
        unity_detail = unity_spider.detailContent([unity_id])
        
        if unity_detail and 'list' in unity_detail and unity_detail['list']:
            unity_video = unity_detail['list'][0]
            
            print(f"  返回结构: {type(unity_detail)}")
            print(f"  视频数量: {len(unity_detail['list'])}")
            print(f"  字段数量: {len(unity_video.keys())}")
            
            # 分析关键字段
            key_fields = ['vod_id', 'vod_name', 'vod_play_from', 'vod_play_url']
            for field in key_fields:
                value = unity_video.get(field, '')
                print(f"  {field}: {'有值' if value else '空'} ({len(str(value))}字符)")
            
            # 分析播放源格式
            play_from = unity_video.get('vod_play_from', '')
            play_url = unity_video.get('vod_play_url', '')
            
            if play_from:
                sources = play_from.split('$$$')
                print(f"  播放源数量: {len(sources)}")
                print(f"  播放源示例: {sources[0] if sources else '无'}")
            
            if play_url:
                url_groups = play_url.split('$$$')
                print(f"  播放URL组数量: {len(url_groups)}")
                if url_groups:
                    first_episodes = url_groups[0].split('#')
                    print(f"  第一组剧集数: {len(first_episodes)}")
                    if first_episodes:
                        print(f"  第一集格式: {first_episodes[0]}")
        else:
            print(f"  ❌ 获取详情失败")
        
        # 获取泥视频详情
        print(f"\n泥视频detailContent分析:")
        nivod_detail = nivod_spider.detailContent([nivod_id])
        
        if nivod_detail and 'list' in nivod_detail and nivod_detail['list']:
            nivod_video = nivod_detail['list'][0]
            
            print(f"  返回结构: {type(nivod_detail)}")
            print(f"  视频数量: {len(nivod_detail['list'])}")
            print(f"  字段数量: {len(nivod_video.keys())}")
            
            # 分析关键字段
            key_fields = ['vod_id', 'vod_name', 'vod_play_from', 'vod_play_url']
            for field in key_fields:
                value = nivod_video.get(field, '')
                print(f"  {field}: {'有值' if value else '空'} ({len(str(value))}字符)")
            
            # 分析播放源格式
            play_from = nivod_video.get('vod_play_from', '')
            play_url = nivod_video.get('vod_play_url', '')
            
            if play_from:
                sources = play_from.split('$$$')
                print(f"  播放源数量: {len(sources)}")
                print(f"  播放源示例: {sources[0] if sources else '无'}")
            
            if play_url:
                url_groups = play_url.split('$$$')
                print(f"  播放URL组数量: {len(url_groups)}")
                if url_groups:
                    first_episodes = url_groups[0].split('#')
                    print(f"  第一组剧集数: {len(first_episodes)}")
                    if first_episodes:
                        print(f"  第一集格式: {first_episodes[0]}")
        else:
            print(f"  ❌ 获取详情失败")
        
        # 对比差异
        print(f"\ndetailContent方法差异总结:")
        if unity_detail and nivod_detail:
            unity_video = unity_detail['list'][0] if unity_detail.get('list') else {}
            nivod_video = nivod_detail['list'][0] if nivod_detail.get('list') else {}
            
            # 字段完整性对比
            unity_fields = set(unity_video.keys())
            nivod_fields = set(nivod_video.keys())
            
            common_fields = unity_fields & nivod_fields
            unity_only = unity_fields - nivod_fields
            nivod_only = nivod_fields - unity_fields
            
            print(f"  共同字段: {len(common_fields)}个")
            print(f"  统一影视独有: {len(unity_only)}个 {list(unity_only)}")
            print(f"  泥视频独有: {len(nivod_only)}个 {list(nivod_only)}")
            
            # URL格式对比
            unity_play_url = unity_video.get('vod_play_url', '')
            nivod_play_url = nivod_video.get('vod_play_url', '')
            
            if unity_play_url and nivod_play_url:
                unity_first = unity_play_url.split('$$$')[0].split('#')[0] if unity_play_url else ''
                nivod_first = nivod_play_url.split('$$$')[0].split('#')[0] if nivod_play_url else ''
                
                print(f"  统一影视URL格式: {unity_first}")
                print(f"  泥视频URL格式: {nivod_first}")
                
                # 分析URL结构
                if '$' in unity_first:
                    unity_name, unity_url = unity_first.split('$', 1)
                    print(f"    统一影视: 名称='{unity_name}', URL='{unity_url}'")
                
                if '$' in nivod_first:
                    nivod_name, nivod_url = nivod_first.split('$', 1)
                    print(f"    泥视频: 名称='{nivod_name}', URL='{nivod_url}'")
        
    except Exception as e:
        print(f"❌ detailContent方法对比失败: {str(e)}")

def compare_player_content_methods(unity_spider, nivod_spider, unity_id, nivod_id):
    """对比playerContent方法"""
    print("playerContent方法实现对比:")
    
    try:
        # 构造测试播放ID
        unity_test_id = f"/index.php/vod/play/id/{unity_id}/sid/3/nid/1.html"
        nivod_test_id = f"{nivod_id}-1-1"
        
        print(f"\n测试播放ID:")
        print(f"  统一影视: {unity_test_id}")
        print(f"  泥视频: {nivod_test_id}")
        
        # 测试统一影视playerContent
        print(f"\n统一影视playerContent分析:")
        try:
            unity_result = unity_spider.playerContent("", unity_test_id, "")
            
            if unity_result:
                print(f"  返回结构: {type(unity_result)}")
                print(f"  字段数量: {len(unity_result.keys())}")
                
                for key, value in unity_result.items():
                    print(f"  {key}: {type(value).__name__} = {str(value)[:60]}{'...' if len(str(value)) > 60 else ''}")
            else:
                print(f"  ❌ 返回空结果")
        except Exception as e:
            print(f"  ❌ 调用失败: {str(e)}")
        
        # 测试泥视频playerContent
        print(f"\n泥视频playerContent分析:")
        try:
            nivod_result = nivod_spider.playerContent("", nivod_test_id, "")
            
            if nivod_result:
                print(f"  返回结构: {type(nivod_result)}")
                print(f"  字段数量: {len(nivod_result.keys())}")
                
                for key, value in nivod_result.items():
                    print(f"  {key}: {type(value).__name__} = {str(value)[:60]}{'...' if len(str(value)) > 60 else ''}")
            else:
                print(f"  ❌ 返回空结果")
        except Exception as e:
            print(f"  ❌ 调用失败: {str(e)}")
        
        # 对比差异
        print(f"\nplayerContent方法差异总结:")
        print(f"  输入格式差异:")
        print(f"    统一影视: 使用完整的播放页面URL路径")
        print(f"    泥视频: 使用简化的ID格式 (vod_id-source-episode)")
        
        print(f"  处理逻辑差异:")
        print(f"    统一影视: 直接访问播放页面，查找iframe和视频链接")
        print(f"    泥视频: 解析ID参数，构造播放URL，提取视频链接")
        
        print(f"  代理机制差异:")
        print(f"    统一影视: 不使用localProxy，直接返回视频链接")
        print(f"    泥视频: 使用localProxy代理M3U8链接")
        
    except Exception as e:
        print(f"❌ playerContent方法对比失败: {str(e)}")

def analyze_tvbox_jump_mechanism(unity_spider, nivod_spider, unity_id, nivod_id):
    """分析TVBox跳转机制"""
    print("TVBox跳转机制深度分析:")

    try:
        # 获取详情数据
        unity_detail = unity_spider.detailContent([unity_id])
        nivod_detail = nivod_spider.detailContent([nivod_id])

        if not (unity_detail and nivod_detail):
            print("❌ 无法获取详情数据")
            return

        unity_video = unity_detail['list'][0]
        nivod_video = nivod_detail['list'][0]

        print(f"\nTVBox跳转流程分析:")
        print(f"1. 用户在TVBox中点击视频详情页的某一集")
        print(f"2. TVBox从vod_play_url中提取对应的播放链接")
        print(f"3. TVBox调用playerContent方法获取真实播放链接")
        print(f"4. TVBox使用返回的链接进行播放")

        # 分析vod_play_url格式
        print(f"\nvod_play_url格式分析:")

        unity_play_url = unity_video.get('vod_play_url', '')
        nivod_play_url = nivod_video.get('vod_play_url', '')

        if unity_play_url:
            unity_groups = unity_play_url.split('$$$')
            if unity_groups:
                unity_episodes = unity_groups[0].split('#')
                print(f"  统一影视格式:")
                print(f"    播放源数: {len(unity_groups)}")
                print(f"    第一源剧集数: {len(unity_episodes)}")

                # 分析前3集的格式
                for i, episode in enumerate(unity_episodes[:3], 1):
                    if '$' in episode:
                        name, url = episode.split('$', 1)
                        print(f"    第{i}集: '{name}' -> '{url}'")

                        # 检查URL是否为可执行路径
                        if url.startswith('/index.php/vod/play/'):
                            print(f"      ✅ 可执行播放页面URL")
                        else:
                            print(f"      ❌ 非标准播放页面URL")

        if nivod_play_url:
            nivod_groups = nivod_play_url.split('$$$')
            if nivod_groups:
                nivod_episodes = nivod_groups[0].split('#')
                print(f"  泥视频格式:")
                print(f"    播放源数: {len(nivod_groups)}")
                print(f"    第一源剧集数: {len(nivod_episodes)}")

                # 分析前3集的格式
                for i, episode in enumerate(nivod_episodes[:3], 1):
                    if '$' in episode:
                        name, url = episode.split('$', 1)
                        print(f"    第{i}集: '{name}' -> '{url}'")

                        # 检查URL是否为可执行路径
                        if url.startswith('/niplay/'):
                            print(f"      ✅ 可执行播放页面URL")
                        else:
                            print(f"      ❌ 非标准播放页面URL")

        # 分析TVBox兼容性要求
        print(f"\nTVBox兼容性要求分析:")
        print(f"1. vod_play_url必须包含可执行的播放页面URL")
        print(f"2. playerContent方法必须能正确解析这些URL")
        print(f"3. 返回的播放链接必须是可直接播放的视频URL")
        print(f"4. parse字段必须正确设置（0=直接播放，1=需要解析）")

        # 测试跳转兼容性
        print(f"\n跳转兼容性测试:")

        # 测试统一影视
        if unity_play_url:
            unity_groups = unity_play_url.split('$$$')
            if unity_groups:
                unity_episodes = unity_groups[0].split('#')
                if unity_episodes and '$' in unity_episodes[0]:
                    _, test_url = unity_episodes[0].split('$', 1)

                    print(f"  统一影视测试:")
                    print(f"    测试URL: {test_url}")

                    try:
                        result = unity_spider.playerContent("", test_url, "")
                        if result:
                            parse_value = result.get('parse', '')
                            url_value = result.get('url', '')
                            print(f"    ✅ playerContent成功")
                            print(f"    parse: {parse_value}")
                            print(f"    url: {url_value[:60]}{'...' if len(url_value) > 60 else ''}")

                            if parse_value == 0 and url_value:
                                print(f"    ✅ TVBox兼容性: 良好")
                            else:
                                print(f"    ❌ TVBox兼容性: 有问题")
                        else:
                            print(f"    ❌ playerContent失败")
                    except Exception as e:
                        print(f"    ❌ 测试失败: {str(e)}")

        # 测试泥视频
        if nivod_play_url:
            nivod_groups = nivod_play_url.split('$$$')
            if nivod_groups:
                nivod_episodes = nivod_groups[0].split('#')
                if nivod_episodes and '$' in nivod_episodes[0]:
                    _, test_url = nivod_episodes[0].split('$', 1)

                    print(f"  泥视频测试:")
                    print(f"    测试URL: {test_url}")

                    # 从URL中提取播放ID
                    play_id_match = re.search(r'/niplay/(\d+-\d+-\d+)/', test_url)
                    if play_id_match:
                        play_id = play_id_match.group(1)
                        print(f"    提取播放ID: {play_id}")

                        try:
                            result = nivod_spider.playerContent("", play_id, "")
                            if result:
                                parse_value = result.get('parse', '')
                                url_value = result.get('url', '')
                                print(f"    ✅ playerContent成功")
                                print(f"    parse: {parse_value}")
                                print(f"    url: {url_value[:60]}{'...' if len(url_value) > 60 else ''}")

                                if parse_value == 0 and url_value:
                                    print(f"    ✅ TVBox兼容性: 良好")
                                else:
                                    print(f"    ❌ TVBox兼容性: 有问题")
                            else:
                                print(f"    ❌ playerContent失败")
                        except Exception as e:
                            print(f"    ❌ 测试失败: {str(e)}")
                    else:
                        print(f"    ❌ 无法提取播放ID")

    except Exception as e:
        print(f"❌ TVBox跳转机制分析失败: {str(e)}")

def identify_specific_issues(unity_spider, nivod_spider, unity_id, nivod_id):
    """识别具体问题"""
    print("具体问题定位和修复方案:")

    issues_found = []

    try:
        # 获取详情数据
        unity_detail = unity_spider.detailContent([unity_id])
        nivod_detail = nivod_spider.detailContent([nivod_id])

        if not (unity_detail and nivod_detail):
            issues_found.append("无法获取详情数据进行对比")
            return

        unity_video = unity_detail['list'][0]
        nivod_video = nivod_detail['list'][0]

        print(f"\n问题检查清单:")

        # 1. 检查URL格式兼容性
        print(f"1. URL格式兼容性检查:")

        unity_play_url = unity_video.get('vod_play_url', '')
        nivod_play_url = nivod_video.get('vod_play_url', '')

        if unity_play_url and nivod_play_url:
            # 分析URL格式差异
            unity_first = unity_play_url.split('$$$')[0].split('#')[0]
            nivod_first = nivod_play_url.split('$$$')[0].split('#')[0]

            if '$' in unity_first and '$' in nivod_first:
                _, unity_url = unity_first.split('$', 1)
                _, nivod_url = nivod_first.split('$', 1)

                print(f"  统一影视URL: {unity_url}")
                print(f"  泥视频URL: {nivod_url}")

                # 检查URL结构
                if '/index.php/vod/play/' in unity_url and '/niplay/' in nivod_url:
                    print(f"  ✅ 两者都使用可执行播放页面URL")
                else:
                    print(f"  ❌ URL格式不一致")
                    issues_found.append("URL格式不一致")

        # 2. 检查playerContent参数传递
        print(f"\n2. playerContent参数传递检查:")

        # 统一影视使用完整URL路径
        # 泥视频需要从URL中提取播放ID

        if nivod_play_url:
            nivod_first = nivod_play_url.split('$$$')[0].split('#')[0]
            if '$' in nivod_first:
                _, nivod_url = nivod_first.split('$', 1)

                # 检查是否能正确提取播放ID
                play_id_match = re.search(r'/niplay/(\d+-\d+-\d+)/', nivod_url)
                if play_id_match:
                    play_id = play_id_match.group(1)
                    print(f"  ✅ 能够从URL提取播放ID: {play_id}")
                else:
                    print(f"  ❌ 无法从URL提取播放ID")
                    issues_found.append("无法从URL提取播放ID")

        # 3. 检查返回数据格式
        print(f"\n3. 返回数据格式检查:")

        # 测试playerContent返回格式
        if nivod_play_url:
            nivod_first = nivod_play_url.split('$$$')[0].split('#')[0]
            if '$' in nivod_first:
                _, nivod_url = nivod_first.split('$', 1)
                play_id_match = re.search(r'/niplay/(\d+-\d+-\d+)/', nivod_url)

                if play_id_match:
                    play_id = play_id_match.group(1)

                    try:
                        result = nivod_spider.playerContent("", play_id, "")

                        if result:
                            required_fields = ['parse', 'url']
                            missing_fields = []

                            for field in required_fields:
                                if field not in result:
                                    missing_fields.append(field)

                            if missing_fields:
                                print(f"  ❌ 缺少必需字段: {missing_fields}")
                                issues_found.append(f"playerContent缺少字段: {missing_fields}")
                            else:
                                print(f"  ✅ 包含所有必需字段")

                                # 检查parse值
                                parse_value = result.get('parse')
                                if parse_value == 0:
                                    print(f"  ✅ parse=0 (直接播放)")
                                elif parse_value == 1:
                                    print(f"  ⚠️ parse=1 (需要解析)")
                                else:
                                    print(f"  ❌ parse值异常: {parse_value}")
                                    issues_found.append(f"parse值异常: {parse_value}")

                                # 检查URL值
                                url_value = result.get('url', '')
                                if url_value and url_value.startswith('http'):
                                    print(f"  ✅ URL格式正确")
                                else:
                                    print(f"  ❌ URL格式错误: {url_value}")
                                    issues_found.append(f"URL格式错误")
                        else:
                            print(f"  ❌ playerContent返回空结果")
                            issues_found.append("playerContent返回空结果")
                    except Exception as e:
                        print(f"  ❌ playerContent调用失败: {str(e)}")
                        issues_found.append(f"playerContent调用失败: {str(e)}")

        # 4. 检查localProxy机制
        print(f"\n4. localProxy机制检查:")

        # 检查泥视频的localProxy实现
        try:
            # 查看localProxy方法实现
            import inspect
            localproxy_source = inspect.getsource(nivod_spider.localProxy)

            if 'pass' in localproxy_source:
                print(f"  ❌ localProxy方法为空实现")
                issues_found.append("localProxy方法未实现")
            else:
                print(f"  ✅ localProxy方法已实现")
        except Exception as e:
            print(f"  ❌ 无法检查localProxy实现: {str(e)}")

        # 总结问题
        print(f"\n🎯 问题总结:")
        if issues_found:
            print(f"发现 {len(issues_found)} 个问题:")
            for i, issue in enumerate(issues_found, 1):
                print(f"  {i}. {issue}")
        else:
            print(f"✅ 未发现明显问题")

        # 提供修复建议
        print(f"\n🔧 修复建议:")
        if "localProxy方法未实现" in issues_found:
            print(f"1. 实现localProxy方法以支持代理功能")

        if any("URL" in issue for issue in issues_found):
            print(f"2. 检查URL格式和参数传递逻辑")

        if any("playerContent" in issue for issue in issues_found):
            print(f"3. 修复playerContent方法的返回格式")

        print(f"4. 参考统一影视的成功实现进行优化")

    except Exception as e:
        print(f"❌ 具体问题定位失败: {str(e)}")

if __name__ == '__main__':
    detailed_code_comparison()
