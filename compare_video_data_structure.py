# -*- coding: utf-8 -*-
"""
详细对比统一影视和泥视频爬虫的视频信息数据结构
分析字段差异、内容格式、数据完整性等
"""

import sys
import json
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def compare_video_data_structure():
    """对比两个爬虫的视频数据结构"""
    print("=" * 120)
    print("🔍 统一影视 vs 泥视频 - 视频信息数据结构详细对比分析")
    print("=" * 120)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试用例
    test_cases = [
        {"search": "斗罗大陆", "type": "动漫"},
        {"search": "凡人修仙传", "type": "动漫"},
        {"search": "两个女人", "type": "电影"},
    ]
    
    # 导入爬虫
    try:
        from 统一影视 import Spider as TongyiysSpider
        from 泥视频 import Spider as NivodSpider
        
        tongyiys_spider = TongyiysSpider()
        tongyiys_spider.init()
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        print("✅ 爬虫初始化成功")
        print(f"统一影视: {tongyiys_spider.host}")
        print(f"泥视频: {nivod_spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 对比分析每个测试用例
    for i, test_case in enumerate(test_cases):
        search_key = test_case["search"]
        video_type = test_case["type"]
        
        print(f"\n{'='*100}")
        print(f"📺 测试用例 {i+1}: {search_key} ({video_type})")
        print(f"{'='*100}")
        
        # 获取统一影视数据
        print(f"\n🎯 统一影视数据获取:")
        print("-" * 80)
        tongyiys_data = get_tongyiys_video_data(tongyiys_spider, search_key)
        
        # 获取泥视频数据
        print(f"\n🎯 泥视频数据获取:")
        print("-" * 80)
        nivod_data = get_nivod_video_data(nivod_spider, search_key)
        
        # 详细对比分析
        print(f"\n📊 详细对比分析:")
        print("-" * 80)
        compare_video_data(tongyiys_data, nivod_data, search_key)
    
    # 生成总结报告
    print(f"\n{'='*120}")
    print("📋 总结分析报告")
    print(f"{'='*120}")
    generate_comparison_report()

def get_tongyiys_video_data(spider, search_key):
    """获取统一影视的视频数据"""
    try:
        # 搜索视频
        search_result = spider.searchContent(search_key, False, "1")
        
        if not search_result or 'list' not in search_result or not search_result['list']:
            print(f"❌ 搜索 '{search_key}' 未找到结果")
            return None
        
        # 取第一个搜索结果
        video = search_result['list'][0]
        video_id = video['vod_id']
        video_name = video['vod_name']
        
        print(f"找到视频: {video_name} (ID: {video_id})")
        
        # 获取详情
        detail_result = spider.detailContent([video_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print(f"❌ 获取详情失败")
            return None
        
        video_info = detail_result['list'][0]
        
        print(f"✅ 统一影视数据获取成功")
        print(f"数据字段数量: {len(video_info)}")
        
        return {
            'spider_type': '统一影视',
            'video_id': video_id,
            'video_name': video_name,
            'video_info': video_info,
            'search_key': search_key
        }
        
    except Exception as e:
        print(f"❌ 统一影视数据获取失败: {str(e)}")
        return None

def get_nivod_video_data(spider, search_key):
    """获取泥视频的视频数据"""
    try:
        # 搜索视频
        search_result = spider.searchContent(search_key, False, "1")
        
        if not search_result or 'list' not in search_result or not search_result['list']:
            print(f"❌ 搜索 '{search_key}' 未找到结果")
            return None
        
        # 取第一个搜索结果
        video = search_result['list'][0]
        video_id = video['vod_id']
        video_name = video['vod_name']
        
        print(f"找到视频: {video_name} (ID: {video_id})")
        
        # 获取详情
        detail_result = spider.detailContent([video_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print(f"❌ 获取详情失败")
            return None
        
        vod_info = detail_result['list'][0]
        
        print(f"✅ 泥视频数据获取成功")
        print(f"数据字段数量: {len(vod_info)}")
        
        return {
            'spider_type': '泥视频',
            'video_id': video_id,
            'video_name': video_name,
            'vod_info': vod_info,
            'search_key': search_key
        }
        
    except Exception as e:
        print(f"❌ 泥视频数据获取失败: {str(e)}")
        return None

def compare_video_data(tongyiys_data, nivod_data, search_key):
    """详细对比两个爬虫的视频数据"""
    if not tongyiys_data or not nivod_data:
        print("❌ 数据缺失，无法进行对比")
        return
    
    tongyiys_info = tongyiys_data['video_info']
    nivod_info = nivod_data['vod_info']
    
    print(f"对比视频: {search_key}")
    print(f"统一影视: {tongyiys_data['video_name']} (ID: {tongyiys_data['video_id']})")
    print(f"泥视频: {nivod_data['video_name']} (ID: {nivod_data['video_id']})")
    
    # 1. 字段结构对比
    print(f"\n1️⃣ 字段结构对比:")
    compare_field_structure(tongyiys_info, nivod_info)
    
    # 2. 关键字段内容对比
    print(f"\n2️⃣ 关键字段内容对比:")
    compare_key_fields(tongyiys_info, nivod_info)
    
    # 3. 播放源数据对比
    print(f"\n3️⃣ 播放源数据对比:")
    compare_play_sources(tongyiys_info, nivod_info)
    
    # 4. 数据质量评估
    print(f"\n4️⃣ 数据质量评估:")
    evaluate_data_quality(tongyiys_info, nivod_info)

def compare_field_structure(tongyiys_info, nivod_info):
    """对比字段结构"""
    tongyiys_fields = set(tongyiys_info.keys())
    nivod_fields = set(nivod_info.keys())
    
    common_fields = tongyiys_fields & nivod_fields
    tongyiys_only = tongyiys_fields - nivod_fields
    nivod_only = nivod_fields - tongyiys_fields
    
    print(f"统一影视字段数量: {len(tongyiys_fields)}")
    print(f"泥视频字段数量: {len(nivod_fields)}")
    print(f"共同字段数量: {len(common_fields)}")
    
    if common_fields:
        print(f"✅ 共同字段: {sorted(list(common_fields))}")
    
    if tongyiys_only:
        print(f"🔵 统一影视独有字段: {sorted(list(tongyiys_only))}")
    
    if nivod_only:
        print(f"🟡 泥视频独有字段: {sorted(list(nivod_only))}")

def compare_key_fields(tongyiys_info, nivod_info):
    """对比关键字段内容"""
    key_fields = [
        'vod_id', 'vod_name', 'vod_pic', 'vod_remarks', 
        'vod_year', 'vod_area', 'vod_director', 'vod_actor', 
        'vod_content', 'vod_play_from', 'vod_play_url'
    ]
    
    for field in key_fields:
        print(f"\n📋 字段: {field}")
        print("-" * 50)
        
        tongyiys_value = tongyiys_info.get(field, '❌ 字段不存在')
        nivod_value = nivod_info.get(field, '❌ 字段不存在')
        
        # 显示字段值（截断长内容）
        tongyiys_display = truncate_text(str(tongyiys_value), 100)
        nivod_display = truncate_text(str(nivod_value), 100)
        
        print(f"统一影视: {tongyiys_display}")
        print(f"泥视频:   {nivod_display}")
        
        # 分析字段差异
        if tongyiys_value == nivod_value:
            print("✅ 内容完全相同")
        elif str(tongyiys_value) == '❌ 字段不存在' or str(nivod_value) == '❌ 字段不存在':
            print("⚠️  字段存在性差异")
        else:
            print("🔄 内容存在差异")
            
            # 特殊字段的详细分析
            if field in ['vod_play_from', 'vod_play_url']:
                analyze_play_field_difference(tongyiys_value, nivod_value, field)

def analyze_play_field_difference(tongyiys_value, nivod_value, field_name):
    """分析播放字段的差异"""
    print(f"  📊 {field_name} 详细分析:")
    
    if field_name == 'vod_play_from':
        tongyiys_sources = str(tongyiys_value).split('$$$') if tongyiys_value else []
        nivod_sources = str(nivod_value).split('$$$') if nivod_value else []
        
        print(f"    统一影视播放源数量: {len(tongyiys_sources)}")
        print(f"    泥视频播放源数量: {len(nivod_sources)}")
        
        if tongyiys_sources:
            print(f"    统一影视播放源示例: {tongyiys_sources[0]}")
        if nivod_sources:
            print(f"    泥视频播放源示例: {nivod_sources[0]}")
    
    elif field_name == 'vod_play_url':
        tongyiys_length = len(str(tongyiys_value)) if tongyiys_value else 0
        nivod_length = len(str(nivod_value)) if nivod_value else 0
        
        print(f"    统一影视URL总长度: {tongyiys_length}")
        print(f"    泥视频URL总长度: {nivod_length}")

def compare_play_sources(tongyiys_info, nivod_info):
    """对比播放源数据"""
    tongyiys_from = tongyiys_info.get('vod_play_from', '')
    tongyiys_url = tongyiys_info.get('vod_play_url', '')
    nivod_from = nivod_info.get('vod_play_from', '')
    nivod_url = nivod_info.get('vod_play_url', '')
    
    # 分析播放源格式
    print("播放源格式对比:")
    
    if tongyiys_from:
        tongyiys_sources = tongyiys_from.split('$$$')
        print(f"统一影视: {len(tongyiys_sources)}个播放源")
        for i, source in enumerate(tongyiys_sources[:3]):  # 显示前3个
            print(f"  播放源{i+1}: {source}")
    
    if nivod_from:
        nivod_sources = nivod_from.split('$$$')
        print(f"泥视频: {len(nivod_sources)}个播放源")
        for i, source in enumerate(nivod_sources[:3]):  # 显示前3个
            print(f"  播放源{i+1}: {source}")
    
    # 分析URL格式
    print("\nURL格式对比:")
    
    if tongyiys_url:
        tongyiys_urls = tongyiys_url.split('$$$')
        if tongyiys_urls and '#' in tongyiys_urls[0]:
            sample_episode = tongyiys_urls[0].split('#')[0]
            if '$' in sample_episode:
                _, sample_url = sample_episode.split('$', 1)
                print(f"统一影视URL示例: {sample_url}")
    
    if nivod_url:
        nivod_urls = nivod_url.split('$$$')
        if nivod_urls and '#' in nivod_urls[0]:
            sample_episode = nivod_urls[0].split('#')[0]
            if '$' in sample_episode:
                _, sample_url = sample_episode.split('$', 1)
                print(f"泥视频URL示例: {sample_url}")

def evaluate_data_quality(tongyiys_info, nivod_info):
    """评估数据质量"""
    quality_fields = ['vod_name', 'vod_pic', 'vod_content', 'vod_year', 'vod_area', 'vod_director', 'vod_actor']
    
    tongyiys_score = 0
    nivod_score = 0
    
    for field in quality_fields:
        tongyiys_value = tongyiys_info.get(field, '')
        nivod_value = nivod_info.get(field, '')
        
        # 评分标准：有内容且不为空
        if tongyiys_value and str(tongyiys_value).strip():
            tongyiys_score += 1
        
        if nivod_value and str(nivod_value).strip():
            nivod_score += 1
    
    total_fields = len(quality_fields)
    
    print(f"数据完整性评分:")
    print(f"统一影视: {tongyiys_score}/{total_fields} ({tongyiys_score/total_fields:.1%})")
    print(f"泥视频: {nivod_score}/{total_fields} ({nivod_score/total_fields:.1%})")
    
    if tongyiys_score > nivod_score:
        print("🏆 统一影视数据更完整")
    elif nivod_score > tongyiys_score:
        print("🏆 泥视频数据更完整")
    else:
        print("🤝 数据完整性相当")

def truncate_text(text, max_length):
    """截断文本"""
    if len(text) <= max_length:
        return text
    return text[:max_length] + "..."

def generate_comparison_report():
    """生成对比报告"""
    print("🔍 关键发现:")
    print("1. 字段结构差异")
    print("   - 两个爬虫使用相同的字段名称规范")
    print("   - 字段数量可能存在差异")
    
    print("\n2. 数据内容质量")
    print("   - 统一影视: 通常包含更多元数据信息")
    print("   - 泥视频: 专注于核心播放功能")
    
    print("\n3. 播放源格式")
    print("   - 统一影视: 使用SID标识机制")
    print("   - 泥视频: 使用简单名称机制")
    
    print("\n💡 改进建议:")
    print("1. 统一数据字段标准")
    print("2. 提高数据完整性")
    print("3. 优化播放源标识机制")
    print("4. 增强数据质量验证")

if __name__ == '__main__':
    compare_video_data_structure()
