# PyramidStore 开发环境说明

## 🎯 环境配置完成状态

✅ **项目分析** - 完成  
✅ **依赖清单** - 完成  
✅ **虚拟环境** - 完成  
✅ **依赖安装** - 完成  
✅ **功能验证** - 完成  

## 📁 项目结构

```
PyramidStore-18-main/
├── base/                    # 核心基础模块
│   ├── spider.py           # 爬虫抽象基类
│   └── localProxy.py       # 本地代理配置
├── plugin/                  # 爬虫插件目录
│   ├── app/                # 移动应用爬虫 (28个)
│   ├── html/               # 网页爬虫 (16个)
│   ├── adult/              # 成人内容爬虫 (12个)
│   ├── official/           # 官方平台爬虫 (4个)
│   ├── tools/              # 工具脚本 (1个)
│   └── 小白调试示例.py      # 开发调试示例
├── venv/                   # Python虚拟环境
├── requirements.txt        # 依赖包清单
├── 模块清单.md             # 完整模块使用约束
├── 开发环境说明.md         # 本文档
├── example.json           # 配置示例
├── spider.md              # 爬虫开发文档
└── README.md              # 项目说明
```

## 🐍 虚拟环境信息

- **Python版本**: 3.11.9
- **虚拟环境路径**: `./venv/`
- **激活状态**: ✅ 已配置完成
- **依赖包数量**: 19个

### 已安装的核心依赖包
```
requests        2.32.4    # HTTP请求库
lxml            6.0.0     # HTML/XML解析
pyquery         2.0.1     # jQuery风格解析
pycryptodome    3.23.0    # 加密解密库
aiohttp         3.12.15   # 异步HTTP库
```

## 🚀 环境激活和使用

### Windows PowerShell
```powershell
# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 验证环境
python -c "import sys; print(sys.prefix)"

# 运行示例
cd plugin
python 小白调试示例.py
```

### Windows CMD
```cmd
# 激活虚拟环境
venv\Scripts\activate.bat

# 验证环境
python -c "import sys; print(sys.prefix)"

# 运行示例
cd plugin
python 小白调试示例.py
```

### Linux/macOS
```bash
# 激活虚拟环境
source venv/bin/activate

# 验证环境
python -c "import sys; print(sys.prefix)"

# 运行示例
cd plugin
python 小白调试示例.py
```

## 🔧 开发工作流程

### 1. 环境准备
```powershell
# 进入项目目录
cd d:\augment-projects\py2\PyramidStore-18-main

# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 验证环境（应显示虚拟环境路径）
python -c "import sys; print(sys.prefix)"
```

### 2. 开发新爬虫
```powershell
# 进入插件目录
cd plugin

# 复制调试示例作为模板
copy 小白调试示例.py 新爬虫名称.py

# 编辑新爬虫文件
# 实现必要的方法：init, homeContent, categoryContent, detailContent, searchContent, playerContent

# 测试运行
python 新爬虫名称.py
```

### 3. 调试和测试
```python
# 在爬虫文件末尾添加测试代码
if __name__ == "__main__":
    sp = Spider()
    # 测试各个功能
    result = sp.init([])                    # 初始化
    result = sp.homeContent(False)          # 首页内容
    result = sp.searchContent("关键词", False, '1')  # 搜索功能
    result = sp.categoryContent('分类ID', '1', False, {})  # 分类内容
    result = sp.detailContent(['视频ID'])    # 详情页面
    result = sp.playerContent("播放源", "播放ID", {})  # 播放链接
    print(result)
```

## 📋 开发约束和规范

### ⚠️ 严格约束
**所有后续开发工作必须严格遵循以下约束：**

1. **模块使用限制**
   - 只能使用`模块清单.md`中列出的模块
   - 禁止引入新的第三方依赖包
   - 禁止修改`requirements.txt`

2. **架构规范**
   - 所有爬虫必须继承`Spider`基类
   - 实现标准的抽象方法接口
   - 使用基类提供的工具方法

3. **文件组织**
   - 爬虫文件放置在`plugin/`目录下
   - 按功能分类到相应子目录
   - 使用UTF-8编码和中文注释

### ✅ 推荐实践
1. 参考现有爬虫实现
2. 使用`小白调试示例.py`进行开发调试
3. 充分利用基类提供的工具方法
4. 添加详细的错误处理和日志

## 🧪 功能验证结果

### 环境验证
- ✅ Python 3.11.9 正常运行
- ✅ 虚拟环境创建成功
- ✅ 所有依赖包安装完成
- ✅ 核心模块导入正常

### 功能验证
- ✅ 示例爬虫运行成功
- ✅ 返回预期的JSON格式数据
- ✅ 基类方法调用正常
- ✅ 网络请求功能正常

### 测试输出示例
```json
{
  "class": [
    {
      "type_name": "穿越",
      "type_id": "穿越"
    }
  ]
}
```

## 📞 技术支持

如遇到环境问题，请检查：
1. Python版本是否为3.11.9
2. 虚拟环境是否正确激活
3. 依赖包是否完整安装
4. 项目路径是否正确

## 🎉 开发环境就绪

**环境配置完成！现在可以开始PyramidStore爬虫开发工作。**

记住：严格遵循模块使用约束，充分利用现有架构，专注于爬虫逻辑实现。
