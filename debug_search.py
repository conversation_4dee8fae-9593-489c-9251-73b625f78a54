# -*- coding: utf-8 -*-
"""
调试搜索功能问题
"""

import sys
import requests
from lxml import etree
import urllib.parse

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def debug_search():
    """调试搜索功能"""
    print("=" * 50)
    print("调试搜索功能问题")
    print("=" * 50)
    
    try:
        # 测试搜索
        keyword = "你好"
        encoded_key = urllib.parse.quote(keyword)
        url = f"https://www.nivod.vip/s/-------------/?wd={encoded_key}"
        
        print(f"搜索关键词: {keyword}")
        print(f"搜索URL: {url}")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)}")
        
        # 解析HTML
        doc = etree.HTML(response.text)
        
        # 查找搜索结果
        print(f"\n分析搜索结果页面结构:")
        
        # 查找所有包含 nivod 链接的元素
        video_links = doc.xpath('//a[contains(@href, "/nivod/")]')
        print(f"找到 {len(video_links)} 个视频链接")
        
        # 查找带title的链接
        title_links = doc.xpath('//a[contains(@href, "/nivod/") and @title]')
        print(f"找到 {len(title_links)} 个带title的视频链接")
        
        # 显示前5个搜索结果
        for i, link in enumerate(video_links[:5]):
            href = link.get('href', '')
            title = link.get('title', '') or link.text or ''
            
            print(f"\n搜索结果 {i+1}:")
            print(f"  链接: {href}")
            print(f"  标题: {title}")
            
            # 查找图片
            img_elements = link.xpath('.//img')
            for j, img in enumerate(img_elements):
                src = img.get('src', '')
                alt = img.get('alt', '')
                print(f"  图片 {j+1}: {src} (alt: {alt})")
        
        # 测试爬虫的搜索方法
        print(f"\n" + "=" * 50)
        print("测试爬虫搜索方法")
        print("=" * 50)
        
        from plugin.html.泥视频 import Spider
        spider = Spider()
        spider.init()
        
        # 直接调用搜索方法
        search_result = spider.searchContent(keyword, True)
        print(f"爬虫搜索结果数: {len(search_result.get('list', []))}")
        
        # 如果没有结果，尝试直接调用 _extract_videos
        print(f"\n直接测试 _extract_videos 方法:")
        videos = spider._extract_videos(doc)
        print(f"_extract_videos 提取到 {len(videos)} 个视频")
        
        for i, video in enumerate(videos[:3]):
            print(f"  视频 {i+1}: {video.get('vod_name', '')} (ID: {video.get('vod_id', '')})")
        
        # 测试过滤方法
        if videos:
            print(f"\n测试搜索过滤:")
            filtered = spider._filter_search_results(videos, keyword)
            print(f"过滤后结果数: {len(filtered)}")
            
            for i, video in enumerate(filtered[:3]):
                print(f"  过滤结果 {i+1}: {video.get('vod_name', '')}")
        
        # 分析HTML内容，查找可能的搜索结果标识
        print(f"\n" + "=" * 50)
        print("分析HTML内容")
        print("=" * 50)
        
        # 查找包含搜索关键词的文本
        search_texts = doc.xpath(f'//text()[contains(., "{keyword}")]')
        print(f"包含'{keyword}'的文本节点数: {len(search_texts)}")
        
        for i, text in enumerate(search_texts[:5]):
            text_content = text.strip()
            if text_content:
                print(f"  文本 {i+1}: {text_content[:100]}...")
        
        # 查找搜索结果数量提示
        result_count_texts = doc.xpath('//text()[contains(., "找到") or contains(., "结果") or contains(., "部影片")]')
        print(f"\n搜索结果数量提示:")
        for text in result_count_texts:
            text_content = text.strip()
            if text_content:
                print(f"  {text_content}")
        
        # 检查是否有"无结果"提示
        no_result_texts = doc.xpath('//text()[contains(., "没有找到") or contains(., "无结果") or contains(., "暂无")]')
        print(f"\n无结果提示:")
        for text in no_result_texts:
            text_content = text.strip()
            if text_content:
                print(f"  {text_content}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_search()
