# -*- coding: utf-8 -*-
"""
分析泥视频爬虫插件的localProxy代理机制问题
对比统一影视的URL编码方式与泥视频的base64编码方式
"""

import sys
import requests
import re
import json
from base64 import b64encode, b64decode
import urllib.parse

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def analyze_nivod_proxy_issue():
    """分析泥视频代理机制问题"""
    print("=" * 80)
    print("🔍 泥视频爬虫插件localProxy代理机制问题分析")
    print("=" * 80)
    
    # 1. 分析当前泥视频传递给localProxy的URL
    print("\n1. 当前泥视频localProxy接收的URL分析")
    print("-" * 50)
    
    # 模拟泥视频的播放链接提取
    test_play_url = "https://www.nivod.vip/niplay/84898-1-1/"
    print(f"播放页面URL: {test_play_url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.nivod.vip/'
        }
        
        response = requests.get(test_play_url, headers=headers, timeout=10)
        if response.status_code == 200:
            html_content = response.text
            
            # 使用修复后的提取逻辑
            extracted_url = extract_play_url_fixed(html_content, test_play_url)
            print(f"提取的播放链接: {extracted_url}")
            
            # 检查是否为真实视频链接
            if extracted_url and ('http' in extracted_url):
                if any(ext in extracted_url.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                    print("✅ 提取的是真实视频直链")
                    
                    # 测试链接可访问性
                    try:
                        video_response = requests.head(extracted_url, headers=headers, timeout=5)
                        print(f"视频链接状态码: {video_response.status_code}")
                        print(f"内容类型: {video_response.headers.get('Content-Type', 'Unknown')}")
                        
                        if video_response.status_code == 200:
                            print("✅ 视频链接可直接访问")
                        else:
                            print("❌ 视频链接无法直接访问")
                    except Exception as e:
                        print(f"❌ 视频链接访问测试失败: {str(e)}")
                else:
                    print("❌ 提取的不是视频直链，而是页面URL")
            else:
                print("❌ 未提取到有效的播放链接")
        else:
            print(f"❌ 播放页面请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 播放页面分析失败: {str(e)}")
    
    # 2. 对比编码方式
    print("\n2. 编码方式对比分析")
    print("-" * 50)
    
    test_video_url = "https://hn.bfvvs.com/play/mbkzB4Xa/index.m3u8"
    
    # Base64编码方式（泥视频当前使用）
    base64_encoded = b64encode(test_video_url.encode('utf-8')).decode('utf-8')
    base64_proxy_url = f"http://127.0.0.1:9978?do=py&url={base64_encoded}&type=m3u8"
    
    # URL编码方式（统一影视使用）
    url_encoded = urllib.parse.quote(test_video_url, safe='')
    url_proxy_url = f"http://127.0.0.1:9978/m3u8?url={url_encoded}"
    
    print(f"原始视频URL: {test_video_url}")
    print(f"\nBase64编码方式（泥视频）:")
    print(f"  编码后: {base64_encoded}")
    print(f"  代理URL: {base64_proxy_url}")
    
    print(f"\nURL编码方式（统一影视）:")
    print(f"  编码后: {url_encoded}")
    print(f"  代理URL: {url_proxy_url}")
    
    # 3. 分析编码方式的差异
    print("\n3. 编码方式差异分析")
    print("-" * 50)
    
    print("Base64编码特点:")
    print("  ✅ 优点: 可以编码任意字符，包括特殊字符")
    print("  ✅ 优点: 编码后字符串相对较短")
    print("  ❌ 缺点: 需要额外的解码步骤")
    print("  ❌ 缺点: 不是标准的URL编码方式")
    
    print("\nURL编码特点:")
    print("  ✅ 优点: 标准的URL编码方式")
    print("  ✅ 优点: 浏览器原生支持")
    print("  ✅ 优点: 更符合HTTP协议规范")
    print("  ❌ 缺点: 编码后字符串可能较长")
    
    # 4. 验证localProxy处理逻辑
    print("\n4. localProxy处理逻辑验证")
    print("-" * 50)
    
    # 模拟localProxy的base64解码
    try:
        decoded_from_base64 = b64decode(base64_encoded).decode('utf-8')
        print(f"Base64解码结果: {decoded_from_base64}")
        print(f"解码是否正确: {'✅' if decoded_from_base64 == test_video_url else '❌'}")
    except Exception as e:
        print(f"❌ Base64解码失败: {str(e)}")
    
    # 模拟URL解码
    try:
        decoded_from_url = urllib.parse.unquote(url_encoded)
        print(f"URL解码结果: {decoded_from_url}")
        print(f"解码是否正确: {'✅' if decoded_from_url == test_video_url else '❌'}")
    except Exception as e:
        print(f"❌ URL解码失败: {str(e)}")
    
    # 5. 问题根因分析
    print("\n5. 问题根因分析")
    print("-" * 50)
    
    print("🔍 当前问题分析:")
    print("1. 泥视频的_extract_play_url方法修复后，现在能正确提取真实视频链接")
    print("2. localProxy方法使用base64解码，这本身没有问题")
    print("3. 关键问题可能在于:")
    print("   - 提取的视频链接是否真的可以直接播放")
    print("   - 代理服务器是否正确处理了m3u8文件")
    print("   - 播放器是否能正确解析代理返回的内容")
    
    print("\n📊 建议的修复方向:")
    print("1. 确保_extract_play_url方法返回的是真实可播放的视频链接")
    print("2. 验证localProxy的m3u8处理逻辑是否正确")
    print("3. 检查代理URL的格式是否符合播放器要求")
    print("4. 考虑是否需要切换到URL编码方式以提高兼容性")

def extract_play_url_fixed(html_content, page_url):
    """修复版的播放链接提取方法"""
    try:
        # 尝试从页面中提取视频链接
        video_patterns = [
            r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
            r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
            r'video\s*:\s*"([^"]+)"',
            r'source\s*:\s*"([^"]+)"',
            r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
        ]

        for pattern in video_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match and ('http' in match or match.startswith('//')):
                    # 处理JSON转义字符
                    clean_url = match.replace('\\/', '/')
                    # 进一步清理可能的转义字符
                    clean_url = clean_url.replace('\\"', '"').replace('\\\\', '\\')
                    return format_url(clean_url)

        # 如果没有找到直接链接，返回页面URL让播放器解析
        return page_url

    except Exception as e:
        print(f"播放链接提取出错: {str(e)}")
        return page_url

def format_url(url):
    """格式化URL为完整地址"""
    if not url:
        return ''
    if url.startswith('//'):
        return 'https:' + url
    elif url.startswith('/'):
        return 'https://www.nivod.vip' + url
    elif url.startswith('http'):
        return url
    return ''

if __name__ == '__main__':
    analyze_nivod_proxy_issue()
