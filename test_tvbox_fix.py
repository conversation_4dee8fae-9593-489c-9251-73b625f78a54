# -*- coding: utf-8 -*-
"""
测试TVBox兼容性修复效果
"""

import sys
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_tvbox_fix():
    """测试TVBox兼容性修复效果"""
    print("=" * 100)
    print("🔧 TVBox兼容性修复效果测试")
    print("=" * 100)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入修复后的泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 修复后的泥视频爬虫初始化成功")
        print(f"主站地址: {spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频
    test_id = "82135"  # 斗罗大陆II绝世唐门
    
    print(f"\n📺 测试视频: 斗罗大陆II绝世唐门 (ID: {test_id})")
    print("-" * 80)
    
    # 1. 测试播放源提取
    print("1️⃣ 测试播放源提取:")
    print("-" * 60)
    test_play_source_extraction(spider, test_id)
    
    # 2. 测试播放链接唯一性
    print("\n2️⃣ 测试播放链接唯一性:")
    print("-" * 60)
    test_play_link_uniqueness(spider, test_id)
    
    # 3. 测试SID标识
    print("\n3️⃣ 测试SID标识:")
    print("-" * 60)
    test_sid_identification(spider, test_id)
    
    # 4. 测试TVBox数据格式
    print("\n4️⃣ 测试TVBox数据格式:")
    print("-" * 60)
    test_tvbox_data_format(spider, test_id)

def test_play_source_extraction(spider, test_id):
    """测试播放源提取"""
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        print(f"播放源: {play_from}")
        print(f"播放URL长度: {len(play_url)}")
        
        # 检查SID标识
        if '[SID:' in play_from:
            print("✅ 播放源包含SID标识")
        else:
            print("❌ 播放源缺少SID标识")
        
        # 解析播放源
        sources = play_from.split('$$$')
        print(f"播放源数量: {len(sources)}")
        
        for i, source in enumerate(sources, 1):
            print(f"  播放源{i}: {source}")
        
    except Exception as e:
        print(f"❌ 播放源提取测试失败: {str(e)}")

def test_play_link_uniqueness(spider, test_id):
    """测试播放链接唯一性"""
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        play_url = video.get('vod_play_url', '')
        
        if not play_url:
            print("❌ 播放URL为空")
            return
        
        # 收集所有播放ID
        all_play_ids = []
        url_groups = play_url.split('$$$')
        
        print(f"播放URL组数量: {len(url_groups)}")
        
        for i, url_group in enumerate(url_groups, 1):
            episodes = url_group.split('#')
            print(f"\n播放源{i} ({len(episodes)}集):")
            
            # 显示前10集
            for j, episode in enumerate(episodes[:10], 1):
                if '$' in episode:
                    ep_name, ep_url = episode.split('$', 1)
                    
                    # 提取播放ID
                    import re
                    play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                    if play_match:
                        play_id = play_match.group(1)
                        all_play_ids.append(play_id)
                        
                        parts = play_id.split('-')
                        if len(parts) >= 3:
                            video_id, source_id, episode_id = parts
                            print(f"  第{j:2d}集: {ep_name[:20]:<20} -> {video_id}-{source_id}-{episode_id}")
        
        # 检查唯一性
        unique_play_ids = set(all_play_ids)
        print(f"\n播放ID唯一性检查:")
        print(f"总播放ID数量: {len(all_play_ids)}")
        print(f"唯一播放ID数量: {len(unique_play_ids)}")
        
        if len(all_play_ids) == len(unique_play_ids):
            print("✅ 所有播放ID都是唯一的")
        else:
            print("❌ 发现重复的播放ID")
            
            # 统计重复情况
            from collections import Counter
            id_counts = Counter(all_play_ids)
            duplicates = {k: v for k, v in id_counts.items() if v > 1}
            
            print("重复的播放ID:")
            for play_id, count in duplicates.items():
                print(f"  {play_id}: 出现{count}次")
        
    except Exception as e:
        print(f"❌ 播放链接唯一性测试失败: {str(e)}")

def test_sid_identification(spider, test_id):
    """测试SID标识"""
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        play_from = video.get('vod_play_from', '')
        
        print(f"播放源格式: {play_from}")
        
        # 检查SID格式
        if '[SID:' in play_from:
            print("✅ 包含SID标识")
            
            # 解析SID
            sources = play_from.split('$$$')
            print(f"SID解析结果:")
            
            for source in sources:
                import re
                sid_match = re.search(r'\[SID:(\d+)\]', source)
                if sid_match:
                    sid = sid_match.group(1)
                    source_name = source.replace(f'[SID:{sid}]', '').strip()
                    print(f"  播放源: {source_name}, SID: {sid}")
                else:
                    print(f"  ❌ SID格式错误: {source}")
        else:
            print("❌ 缺少SID标识")
        
    except Exception as e:
        print(f"❌ SID标识测试失败: {str(e)}")

def test_tvbox_data_format(spider, test_id):
    """测试TVBox数据格式"""
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        
        # 检查必需字段
        required_fields = {
            'vod_id': '视频ID',
            'vod_name': '视频名称',
            'vod_pic': '视频图片',
            'vod_content': '视频简介',
            'vod_play_from': '播放源',
            'vod_play_url': '播放链接'
        }
        
        print(f"TVBox数据格式检查:")
        all_good = True
        
        for field, desc in required_fields.items():
            value = video.get(field, '')
            status = "✅" if value else "❌"
            print(f"  {status} {field} ({desc}): {'有值' if value else '缺失'}")
            
            if not value:
                all_good = False
        
        # 测试播放链接获取
        play_url = video.get('vod_play_url', '')
        if play_url:
            url_groups = play_url.split('$$$')
            if url_groups:
                first_group = url_groups[0]
                episodes = first_group.split('#')
                
                if episodes:
                    first_episode = episodes[0]
                    if '$' in first_episode:
                        ep_name, ep_url = first_episode.split('$', 1)
                        
                        # 提取播放ID
                        import re
                        play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                        if play_match:
                            play_id = play_match.group(1)
                            
                            print(f"\n播放链接获取测试:")
                            print(f"  测试剧集: {ep_name}")
                            print(f"  播放ID: {play_id}")
                            
                            # 获取播放链接
                            play_result = spider.playerContent("", play_id, "")
                            
                            if play_result:
                                url = play_result.get('url', '')
                                parse = play_result.get('parse', '')
                                
                                print(f"  ✅ 播放链接获取成功")
                                print(f"    URL: {url[:60]}{'...' if len(url) > 60 else ''}")
                                print(f"    Parse: {parse}")
                                
                                if url and url.startswith('http'):
                                    print(f"    ✅ URL格式正确")
                                else:
                                    print(f"    ❌ URL格式错误")
                                    all_good = False
                            else:
                                print(f"  ❌ 播放链接获取失败")
                                all_good = False
        
        # 总结
        if all_good:
            print(f"\n🎯 TVBox兼容性测试结果: ✅ 全部通过")
            print(f"修复效果: 泥视频爬虫现在应该能够在TVBox中正常工作")
        else:
            print(f"\n🎯 TVBox兼容性测试结果: ❌ 仍有问题")
        
    except Exception as e:
        print(f"❌ TVBox数据格式测试失败: {str(e)}")

if __name__ == '__main__':
    test_tvbox_fix()
