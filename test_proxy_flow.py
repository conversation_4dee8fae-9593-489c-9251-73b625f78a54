# -*- coding: utf-8 -*-
"""
测试代理流程 - 分析localProxy参数传递
"""

import sys
import base64
import urllib.parse
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_proxy_flow():
    """测试代理流程"""
    print("=" * 100)
    print("🔍 代理流程分析")
    print("=" * 100)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 泥视频爬虫初始化成功")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 1. 获取播放链接
    print(f"\n1️⃣ 获取播放链接:")
    print("-" * 60)
    
    try:
        play_id = "82135-1-1"
        result = spider.playerContent("", play_id, "")
        
        if result:
            proxy_url = result.get('url', '')
            print(f"✅ 播放链接获取成功")
            print(f"代理URL: {proxy_url}")
            
            # 2. 分析代理URL结构
            print(f"\n2️⃣ 分析代理URL结构:")
            print("-" * 60)
            analyze_proxy_url(proxy_url)
            
            # 3. 模拟框架调用localProxy
            print(f"\n3️⃣ 模拟框架调用localProxy:")
            print("-" * 60)
            simulate_framework_call(spider, proxy_url)
            
        else:
            print(f"❌ 播放链接获取失败")
            
    except Exception as e:
        print(f"❌ 播放链接获取出错: {str(e)}")

def analyze_proxy_url(proxy_url):
    """分析代理URL结构"""
    try:
        from urllib.parse import urlparse, parse_qs
        
        parsed = urlparse(proxy_url)
        query_params = parse_qs(parsed.query)
        
        print(f"URL解析结果:")
        print(f"  协议: {parsed.scheme}")
        print(f"  主机: {parsed.netloc}")
        print(f"  路径: {parsed.path}")
        print(f"  查询字符串: {parsed.query}")
        print(f"  查询参数: {query_params}")
        
        if 'url' in query_params:
            encoded_url = query_params['url'][0]
            print(f"\n编码分析:")
            print(f"  编码URL: {encoded_url}")
            
            # 尝试URL解码
            try:
                decoded_url = urllib.parse.unquote(encoded_url)
                print(f"  URL解码: {decoded_url}")
                
                if decoded_url.startswith('http'):
                    print(f"  ✅ URL解码成功，是有效的HTTP链接")
                else:
                    print(f"  ❌ URL解码后不是有效的HTTP链接")
            except Exception as e:
                print(f"  ❌ URL解码失败: {str(e)}")
            
            # 尝试Base64解码
            try:
                base64_decoded = base64.b64decode(encoded_url).decode('utf-8')
                print(f"  Base64解码: {base64_decoded}")
                
                if base64_decoded.startswith('http'):
                    print(f"  ✅ Base64解码成功，是有效的HTTP链接")
                else:
                    print(f"  ❌ Base64解码后不是有效的HTTP链接")
            except Exception as e:
                print(f"  ❌ Base64解码失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ URL分析失败: {str(e)}")

def simulate_framework_call(spider, proxy_url):
    """模拟框架调用localProxy"""
    try:
        from urllib.parse import urlparse, parse_qs
        
        # 解析代理URL
        parsed = urlparse(proxy_url)
        query_params = parse_qs(parsed.query)
        
        if 'url' in query_params:
            encoded_url = query_params['url'][0]
            
            print(f"模拟不同的参数传递方式:")
            
            # 方式1：直接传递编码的URL字符串
            print(f"\n方式1: 直接传递编码URL字符串")
            try:
                result1 = spider.localProxy(encoded_url)
                print(f"  结果: {type(result1)} - {'成功' if result1 else '失败'}")
                if result1:
                    print(f"  状态码: {result1[0] if isinstance(result1, list) and len(result1) > 0 else 'N/A'}")
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
            
            # 方式2：传递解码后的URL
            print(f"\n方式2: 传递URL解码后的URL")
            try:
                decoded_url = urllib.parse.unquote(encoded_url)
                result2 = spider.localProxy(decoded_url)
                print(f"  结果: {type(result2)} - {'成功' if result2 else '失败'}")
                if result2:
                    print(f"  状态码: {result2[0] if isinstance(result2, list) and len(result2) > 0 else 'N/A'}")
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
            
            # 方式3：传递字典参数
            print(f"\n方式3: 传递字典参数")
            try:
                param_dict = {'url': encoded_url}
                result3 = spider.localProxy(param_dict)
                print(f"  结果: {type(result3)} - {'成功' if result3 else '失败'}")
                if result3:
                    print(f"  状态码: {result3[0] if isinstance(result3, list) and len(result3) > 0 else 'N/A'}")
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
            
            # 方式4：传递字典参数（解码后的URL）
            print(f"\n方式4: 传递字典参数（解码后的URL）")
            try:
                decoded_url = urllib.parse.unquote(encoded_url)
                param_dict = {'url': decoded_url}
                result4 = spider.localProxy(param_dict)
                print(f"  结果: {type(result4)} - {'成功' if result4 else '失败'}")
                if result4:
                    print(f"  状态码: {result4[0] if isinstance(result4, list) and len(result4) > 0 else 'N/A'}")
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
            
            # 方式5：模拟PyramidStore框架的实际调用方式
            print(f"\n方式5: 模拟PyramidStore框架调用")
            try:
                # 检查框架是否会进行Base64编码
                decoded_url = urllib.parse.unquote(encoded_url)
                base64_encoded = base64.b64encode(decoded_url.encode('utf-8')).decode('utf-8')
                
                print(f"  原始URL: {decoded_url}")
                print(f"  Base64编码: {base64_encoded}")
                
                # 测试Base64编码的参数
                result5 = spider.localProxy(base64_encoded)
                print(f"  结果: {type(result5)} - {'成功' if result5 else '失败'}")
                if result5:
                    print(f"  状态码: {result5[0] if isinstance(result5, list) and len(result5) > 0 else 'N/A'}")
                    
            except Exception as e:
                print(f"  ❌ 失败: {str(e)}")
        
        # 总结
        print(f"\n🎯 测试总结:")
        print(f"根据测试结果，确定正确的localProxy参数格式")
        
    except Exception as e:
        print(f"❌ 框架调用模拟失败: {str(e)}")

if __name__ == '__main__':
    test_proxy_flow()
