# -*- coding: utf-8 -*-
"""
测试修复后的泥视频爬虫代理机制
验证URL编码方式是否正确工作
"""

import sys
import urllib.parse

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_fixed_proxy():
    """测试修复后的代理机制"""
    print("=" * 80)
    print("🔧 测试修复后的泥视频代理机制")
    print("=" * 80)
    
    # 导入修复后的泥视频爬虫
    try:
        from 泥视频 import Spider
        spider = Spider()
        spider.init()
        print("✅ 修复后的泥视频爬虫初始化成功")
    except Exception as e:
        print(f"❌ 泥视频爬虫初始化失败: {str(e)}")
        return
    
    # 1. 测试代理URL生成
    print("\n1. 测试代理URL生成（修复后）")
    print("-" * 50)
    
    test_video_url = "https://hn.bfvvs.com/play/mbkzB4Xa/index.m3u8"
    proxy_url = spider._get_proxy_url(test_video_url, 'm3u8')
    
    print(f"原始视频URL: {test_video_url}")
    print(f"生成的代理URL: {proxy_url}")
    
    # 验证代理URL格式
    expected_format = "http://127.0.0.1:9978/m3u8?url="
    if proxy_url.startswith(expected_format):
        print("✅ 代理URL格式正确")
        
        # 提取编码的URL
        encoded_part = proxy_url[len(expected_format):]
        print(f"URL编码部分: {encoded_part}")
        
        # 验证解码
        try:
            decoded_url = urllib.parse.unquote(encoded_part)
            print(f"解码后的URL: {decoded_url}")
            
            if decoded_url == test_video_url:
                print("✅ URL编码/解码正确")
            else:
                print("❌ URL编码/解码不匹配")
        except Exception as e:
            print(f"❌ URL解码失败: {str(e)}")
    else:
        print("❌ 代理URL格式不正确")
        print(f"期望格式: {expected_format}...")
    
    # 2. 测试localProxy方法
    print("\n2. 测试localProxy方法（修复后）")
    print("-" * 50)
    
    # 使用URL编码的参数
    encoded_url = urllib.parse.quote(test_video_url, safe='')
    proxy_param = {
        'url': encoded_url,
        'type': 'm3u8'
    }
    
    print(f"测试参数: {proxy_param}")
    
    try:
        proxy_result = spider.localProxy(proxy_param)
        print(f"localProxy返回结果类型: {type(proxy_result)}")
        
        if proxy_result and len(proxy_result) >= 3:
            status_code = proxy_result[0]
            content_type = proxy_result[1]
            content = proxy_result[2]
            
            print(f"状态码: {status_code}")
            print(f"内容类型: {content_type}")
            print(f"内容长度: {len(content) if content else 0}")
            
            if status_code == 200 and content:
                print("✅ localProxy成功处理了m3u8文件")
                
                # 分析m3u8内容
                if isinstance(content, bytes):
                    content_str = content.decode('utf-8')
                else:
                    content_str = str(content)
                
                lines = content_str.split('\n')[:10]  # 只显示前10行
                print("m3u8内容预览:")
                for i, line in enumerate(lines):
                    print(f"  {i+1}: {line}")
                
                # 检查代理URL格式
                proxy_lines = [line for line in content_str.split('\n') if 'http://127.0.0.1:9978' in line]
                if proxy_lines:
                    sample_proxy = proxy_lines[0]
                    print(f"\n代理URL示例: {sample_proxy}")
                    
                    # 验证代理URL格式
                    if '/ts?url=' in sample_proxy:
                        print("✅ TS片段代理URL格式正确")
                    else:
                        print("❌ TS片段代理URL格式不正确")
                
                proxy_count = len(proxy_lines)
                print(f"包含代理URL的行数: {proxy_count}")
                
                if proxy_count > 0:
                    print("✅ m3u8文件中的TS片段已正确代理")
                else:
                    print("❌ m3u8文件中的TS片段未被代理")
            else:
                print("❌ localProxy处理失败")
        else:
            print("❌ localProxy返回格式错误")
            
    except Exception as e:
        print(f"❌ localProxy测试失败: {str(e)}")
    
    # 3. 测试完整的播放流程
    print("\n3. 测试完整的播放流程（修复后）")
    print("-" * 50)
    
    test_id = "84898-1-1"
    try:
        play_result = spider.playerContent("自营1线", test_id, [])
        print(f"播放结果: {play_result}")
        
        if play_result and 'url' in play_result:
            proxy_url = play_result['url']
            print(f"最终代理URL: {proxy_url}")
            
            # 验证最终代理URL格式
            if proxy_url.startswith("http://127.0.0.1:9978/m3u8?url="):
                print("✅ 最终代理URL格式正确")
                
                # 提取并验证编码的URL
                encoded_part = proxy_url[len("http://127.0.0.1:9978/m3u8?url="):]
                try:
                    decoded_url = urllib.parse.unquote(encoded_part)
                    print(f"解码后的视频URL: {decoded_url}")
                    
                    if any(ext in decoded_url.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                        print("✅ 解码后的URL是真实视频链接")
                    else:
                        print("❌ 解码后的URL不是视频链接")
                except Exception as e:
                    print(f"❌ URL解码失败: {str(e)}")
            else:
                print("❌ 最终代理URL格式不正确")
                print(f"期望格式: http://127.0.0.1:9978/m3u8?url=...")
        else:
            print("❌ 未获取到播放链接")
            
    except Exception as e:
        print(f"❌ 播放流程测试失败: {str(e)}")
    
    # 4. 对比修复前后的差异
    print("\n4. 修复前后对比")
    print("-" * 50)
    
    print("修复前（Base64编码）:")
    print("  格式: http://127.0.0.1:9978?do=py&url={base64_encoded}&type=m3u8")
    print("  问题: 非标准格式，播放器兼容性差")
    
    print("\n修复后（URL编码）:")
    print("  格式: http://127.0.0.1:9978/m3u8?url={url_encoded}")
    print("  优势: 标准HTTP格式，更好的播放器兼容性")
    
    print("\n🎯 修复总结:")
    print("1. ✅ 改用标准URL编码替代Base64编码")
    print("2. ✅ 修改代理URL格式为标准HTTP格式")
    print("3. ✅ localProxy方法正确处理URL编码参数")
    print("4. ✅ 保持m3u8文件处理逻辑完整")
    print("5. ✅ 提高与TVBox等播放器的兼容性")

if __name__ == '__main__':
    test_fixed_proxy()
