# -*- coding: utf-8 -*-
"""
测试TVBox兼容性修复效果
"""

import sys
import base64
import urllib.parse
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_tvbox_compatibility_fix():
    """测试TVBox兼容性修复效果"""
    print("=" * 120)
    print("🎯 TVBox兼容性修复效果测试")
    print("=" * 120)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入修复后的泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 修复后的泥视频爬虫初始化成功")
        print(f"主站地址: {spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频
    test_id = "82135"  # 斗罗大陆II绝世唐门
    
    print(f"\n📺 测试视频: 斗罗大陆II绝世唐门 (ID: {test_id})")
    print("-" * 80)
    
    # 1. 测试代理URL格式
    print("1️⃣ 测试代理URL格式:")
    print("-" * 60)
    test_proxy_url_format(spider, test_id)
    
    # 2. 测试playerContent返回格式
    print("\n2️⃣ 测试playerContent返回格式:")
    print("-" * 60)
    test_player_content_format(spider, test_id)
    
    # 3. 测试localProxy标准参数处理
    print("\n3️⃣ 测试localProxy标准参数处理:")
    print("-" * 60)
    test_local_proxy_standard_params(spider, test_id)
    
    # 4. 完整TVBox流程验证
    print("\n4️⃣ 完整TVBox流程验证:")
    print("-" * 60)
    test_complete_tvbox_flow(spider, test_id)
    
    # 5. 与成功案例对比
    print("\n5️⃣ 与成功案例对比:")
    print("-" * 60)
    compare_with_success_case(spider)

def test_proxy_url_format(spider, test_id):
    """测试代理URL格式"""
    try:
        # 测试代理URL生成
        test_url = "https://example.com/test.m3u8"
        proxy_url = spider._get_proxy_url(test_url, 'm3u8')
        
        print(f"测试URL: {test_url}")
        print(f"生成的代理URL: {proxy_url}")
        
        # 验证格式
        expected_patterns = [
            "http://127.0.0.1:9978",
            "?do=py",
            "&url=",
            "&type=m3u8"
        ]
        
        format_correct = True
        for pattern in expected_patterns:
            if pattern not in proxy_url:
                print(f"  ❌ 缺少必需模式: {pattern}")
                format_correct = False
            else:
                print(f"  ✅ 包含必需模式: {pattern}")
        
        if format_correct:
            print(f"  🎯 代理URL格式: ✅ 符合PyramidStore标准")
            
            # 验证Base64编码
            try:
                url_param = proxy_url.split('&url=')[1].split('&')[0]
                decoded = base64.b64decode(url_param).decode('utf-8')
                if decoded == test_url:
                    print(f"  ✅ Base64编码正确")
                else:
                    print(f"  ❌ Base64编码错误")
            except Exception as e:
                print(f"  ❌ Base64解码失败: {str(e)}")
        else:
            print(f"  🎯 代理URL格式: ❌ 不符合标准")
        
    except Exception as e:
        print(f"❌ 代理URL格式测试失败: {str(e)}")

def test_player_content_format(spider, test_id):
    """测试playerContent返回格式"""
    try:
        play_id = f"{test_id}-1-1"
        result = spider.playerContent("", play_id, "")
        
        if result:
            print(f"playerContent返回结果:")
            for key, value in result.items():
                if key == 'url' and len(str(value)) > 60:
                    print(f"  {key}: {str(value)[:60]}...")
                else:
                    print(f"  {key}: {value}")
            
            # 检查必需字段
            required_fields = ['parse', 'playUrl', 'url', 'header']
            format_correct = True
            
            for field in required_fields:
                if field in result:
                    print(f"  ✅ 包含必需字段: {field}")
                else:
                    print(f"  ❌ 缺少必需字段: {field}")
                    format_correct = False
            
            # 检查字段值
            if result.get('parse') == 0:
                print(f"  ✅ parse=0 (直接播放)")
            else:
                print(f"  ❌ parse={result.get('parse')} (应该为0)")
                format_correct = False
            
            if result.get('playUrl') == '':
                print(f"  ✅ playUrl为空字符串")
            else:
                print(f"  ❌ playUrl={result.get('playUrl')} (应该为空字符串)")
                format_correct = False
            
            if result.get('header') == {}:
                print(f"  ✅ header为空字典")
            else:
                print(f"  ❌ header={result.get('header')} (应该为空字典)")
                format_correct = False
            
            if format_correct:
                print(f"  🎯 playerContent格式: ✅ 符合TVBox要求")
            else:
                print(f"  🎯 playerContent格式: ❌ 不符合TVBox要求")
        else:
            print(f"❌ playerContent返回空结果")
        
    except Exception as e:
        print(f"❌ playerContent格式测试失败: {str(e)}")

def test_local_proxy_standard_params(spider, test_id):
    """测试localProxy标准参数处理"""
    try:
        # 获取真实的代理URL
        play_id = f"{test_id}-1-1"
        result = spider.playerContent("", play_id, "")
        
        if not result or 'url' not in result:
            print(f"❌ 无法获取代理URL")
            return
        
        proxy_url = result['url']
        
        # 解析代理URL参数
        if '&url=' in proxy_url and '&type=' in proxy_url:
            url_param = proxy_url.split('&url=')[1].split('&')[0]
            type_param = proxy_url.split('&type=')[1].split('&')[0]
            
            print(f"解析的参数:")
            print(f"  url参数: {url_param[:50]}...")
            print(f"  type参数: {type_param}")
            
            # 构建标准参数格式
            standard_param = {
                'url': url_param,
                'type': type_param
            }
            
            print(f"\n测试localProxy标准参数格式:")
            print(f"  参数: {standard_param}")
            
            # 调用localProxy
            proxy_result = spider.localProxy(standard_param)
            
            if proxy_result and isinstance(proxy_result, list) and len(proxy_result) >= 3:
                status_code = proxy_result[0]
                content_type = proxy_result[1]
                content = proxy_result[2]
                
                print(f"  ✅ localProxy调用成功")
                print(f"  状态码: {status_code}")
                print(f"  内容类型: {content_type}")
                print(f"  内容大小: {len(content)}字节")
                
                if status_code == 200 and content:
                    print(f"  🎯 localProxy处理: ✅ 成功")
                else:
                    print(f"  🎯 localProxy处理: ❌ 失败")
            else:
                print(f"  ❌ localProxy返回格式错误")
                print(f"  🎯 localProxy处理: ❌ 失败")
        else:
            print(f"❌ 代理URL格式不正确，无法解析参数")
        
    except Exception as e:
        print(f"❌ localProxy标准参数测试失败: {str(e)}")

def test_complete_tvbox_flow(spider, test_id):
    """测试完整TVBox流程"""
    try:
        print(f"模拟TVBox完整播放流程:")
        
        # 步骤1：获取视频详情
        print(f"  步骤1: 获取视频详情")
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print(f"    ❌ 获取详情失败")
            return False
        
        video = detail_result['list'][0]
        play_url = video.get('vod_play_url', '')
        play_from = video.get('vod_play_from', '')
        
        print(f"    ✅ 获取详情成功")
        print(f"    播放源: {play_from}")
        
        # 步骤2：解析播放链接
        print(f"  步骤2: 解析播放链接")
        if not play_url:
            print(f"    ❌ 播放URL为空")
            return False
        
        url_groups = play_url.split('$$$')
        if not url_groups:
            print(f"    ❌ 播放链接解析失败")
            return False
        
        first_group = url_groups[0]
        episodes = first_group.split('#')
        
        if not episodes or '$' not in episodes[0]:
            print(f"    ❌ 剧集格式错误")
            return False
        
        ep_name, ep_url = episodes[0].split('$', 1)
        print(f"    ✅ 解析成功: {ep_name} -> {ep_url}")
        
        # 步骤3：提取播放ID
        print(f"  步骤3: 提取播放ID")
        import re
        play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
        
        if not play_match:
            print(f"    ❌ 播放ID提取失败")
            return False
        
        play_id = play_match.group(1)
        print(f"    ✅ 播放ID: {play_id}")
        
        # 步骤4：调用playerContent
        print(f"  步骤4: 调用playerContent")
        player_result = spider.playerContent("", play_id, "")
        
        if not player_result:
            print(f"    ❌ playerContent失败")
            return False
        
        proxy_url = player_result.get('url', '')
        parse_value = player_result.get('parse', '')
        play_url_value = player_result.get('playUrl', '')
        header_value = player_result.get('header', {})
        
        print(f"    ✅ playerContent成功")
        print(f"    Parse: {parse_value}")
        print(f"    PlayUrl: '{play_url_value}'")
        print(f"    Header: {header_value}")
        print(f"    URL: {proxy_url[:60]}{'...' if len(proxy_url) > 60 else ''}")
        
        # 验证格式
        format_issues = []
        if parse_value != 0:
            format_issues.append(f"parse应为0，实际为{parse_value}")
        if play_url_value != '':
            format_issues.append(f"playUrl应为空字符串，实际为'{play_url_value}'")
        if header_value != {}:
            format_issues.append(f"header应为空字典，实际为{header_value}")
        
        if format_issues:
            print(f"    ❌ 格式问题: {'; '.join(format_issues)}")
            return False
        
        # 步骤5：处理代理请求
        print(f"  步骤5: 处理代理请求")
        if proxy_url and '?do=py&url=' in proxy_url:
            # 解析代理URL参数
            url_param = proxy_url.split('&url=')[1].split('&')[0]
            type_param = proxy_url.split('&type=')[1].split('&')[0] if '&type=' in proxy_url else 'm3u8'
            
            # 构建标准参数
            param = {'url': url_param, 'type': type_param}
            
            # 调用localProxy
            proxy_result = spider.localProxy(param)
            
            if proxy_result and isinstance(proxy_result, list) and len(proxy_result) >= 3:
                status_code = proxy_result[0]
                content_type = proxy_result[1]
                content = proxy_result[2]
                
                print(f"    ✅ 代理请求成功")
                print(f"    状态码: {status_code}")
                print(f"    内容类型: {content_type}")
                print(f"    内容大小: {len(content)}字节")
                
                if status_code == 200 and content:
                    print(f"  🎯 TVBox流程验证: ✅ 完全成功")
                    return True
                else:
                    print(f"  🎯 TVBox流程验证: ❌ 代理数据获取失败")
                    return False
            else:
                print(f"    ❌ 代理请求失败")
                print(f"  🎯 TVBox流程验证: ❌ 代理处理失败")
                return False
        else:
            print(f"    ❌ 代理URL格式不正确")
            print(f"  🎯 TVBox流程验证: ❌ 代理URL格式错误")
            return False
        
    except Exception as e:
        print(f"❌ TVBox流程验证失败: {str(e)}")
        return False

def compare_with_success_case(spider):
    """与成功案例对比"""
    try:
        print(f"与PyramidStore成功案例对比:")
        
        # 对比代理URL格式
        test_url = "https://example.com/test.m3u8"
        nivod_proxy = spider._get_proxy_url(test_url, 'm3u8')
        
        print(f"\n代理URL格式对比:")
        print(f"  泥视频: {nivod_proxy}")
        print(f"  标准格式: http://127.0.0.1:9978?do=py&url={base64.b64encode(test_url.encode()).decode()}&type=m3u8")
        
        # 检查是否匹配
        if '?do=py&url=' in nivod_proxy and '&type=m3u8' in nivod_proxy:
            print(f"  ✅ 格式匹配成功")
        else:
            print(f"  ❌ 格式不匹配")
        
        # 对比playerContent格式
        print(f"\nplayerContent格式对比:")
        print(f"  成功案例格式: {{'parse': 0, 'playUrl': '', 'url': 'video_url', 'header': {{}}}}")
        
        test_result = spider.playerContent("", "82135-1-1", "")
        if test_result:
            actual_format = {
                'parse': test_result.get('parse'),
                'playUrl': test_result.get('playUrl'),
                'url': 'proxy_url' if test_result.get('url') else None,
                'header': test_result.get('header')
            }
            print(f"  泥视频格式: {actual_format}")
            
            if (actual_format['parse'] == 0 and 
                actual_format['playUrl'] == '' and 
                actual_format['header'] == {}):
                print(f"  ✅ 格式匹配成功")
            else:
                print(f"  ❌ 格式不匹配")
        
        print(f"\n🎯 总体兼容性评估:")
        print(f"  修复后的泥视频爬虫现在应该与TVBox完全兼容")
        print(f"  所有关键格式都已调整为PyramidStore标准")
        
    except Exception as e:
        print(f"❌ 成功案例对比失败: {str(e)}")

if __name__ == '__main__':
    test_tvbox_compatibility_fix()
