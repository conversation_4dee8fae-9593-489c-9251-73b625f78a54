# -*- coding: utf-8 -*-
"""
测试直接播放链接的有效性
"""

import sys
import requests
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_direct_playback():
    """测试直接播放链接的有效性"""
    print("=" * 120)
    print("🎬 直接播放链接有效性测试")
    print("=" * 120)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 泥视频爬虫初始化成功")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频
    test_id = "82135"  # 斗罗大陆II绝世唐门
    
    print(f"\n📺 测试视频: 斗罗大陆II绝世唐门 (ID: {test_id})")
    print("-" * 80)
    
    # 1. 获取播放链接
    print("1️⃣ 获取播放链接:")
    print("-" * 60)
    
    try:
        play_id = f"{test_id}-1-1"
        result = spider.playerContent("", play_id, "")
        
        if not result:
            print(f"❌ 无法获取播放链接")
            return
        
        video_url = result.get('url', '')
        parse_value = result.get('parse', '')
        
        print(f"播放链接: {video_url}")
        print(f"Parse值: {parse_value}")
        print(f"链接类型: {'直接播放链接' if parse_value == 0 else '需要解析的链接'}")
        
        if not video_url:
            print(f"❌ 播放链接为空")
            return
        
        # 2. 测试链接可访问性
        print(f"\n2️⃣ 测试链接可访问性:")
        print("-" * 60)
        test_url_accessibility(video_url)
        
        # 3. 分析链接格式
        print(f"\n3️⃣ 分析链接格式:")
        print("-" * 60)
        analyze_url_format(video_url)
        
        # 4. 对比统一影视的链接格式
        print(f"\n4️⃣ 对比统一影视的链接格式:")
        print("-" * 60)
        compare_with_unity_format(video_url)
        
        # 5. TVBox兼容性分析
        print(f"\n5️⃣ TVBox兼容性分析:")
        print("-" * 60)
        analyze_tvbox_compatibility(result)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_url_accessibility(url):
    """测试URL可访问性"""
    try:
        print(f"测试URL: {url[:80]}{'...' if len(url) > 80 else ''}")
        
        # 发送HEAD请求测试可访问性
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        response = requests.head(url, headers=headers, timeout=10, allow_redirects=True)
        
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"Content-Length: {response.headers.get('Content-Length', 'N/A')}")
        
        if response.status_code == 200:
            print(f"✅ 链接可访问")
            
            # 如果是m3u8文件，尝试获取部分内容
            if '.m3u8' in url:
                try:
                    content_response = requests.get(url, headers=headers, timeout=10)
                    if content_response.status_code == 200:
                        content = content_response.text
                        lines = content.strip().split('\n')[:10]  # 只显示前10行
                        
                        print(f"M3U8内容预览:")
                        for i, line in enumerate(lines):
                            print(f"  {i+1}: {line}")
                        
                        # 检查是否包含TS片段
                        ts_count = sum(1 for line in content.split('\n') if line.strip() and not line.startswith('#'))
                        print(f"TS片段数量: {ts_count}")
                        
                        if ts_count > 0:
                            print(f"✅ M3U8文件格式正确，包含{ts_count}个TS片段")
                        else:
                            print(f"❌ M3U8文件格式异常，无TS片段")
                    else:
                        print(f"❌ 无法获取M3U8内容，状态码: {content_response.status_code}")
                except Exception as e:
                    print(f"❌ 获取M3U8内容失败: {str(e)}")
        else:
            print(f"❌ 链接不可访问，状态码: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 可访问性测试失败: {str(e)}")

def analyze_url_format(url):
    """分析URL格式"""
    try:
        print(f"URL分析:")
        
        # 基本信息
        if url.startswith('http://'):
            print(f"  协议: HTTP")
        elif url.startswith('https://'):
            print(f"  协议: HTTPS")
        else:
            print(f"  协议: 未知")
        
        # 域名分析
        from urllib.parse import urlparse
        parsed = urlparse(url)
        print(f"  域名: {parsed.netloc}")
        print(f"  路径: {parsed.path}")
        print(f"  查询参数: {parsed.query if parsed.query else '无'}")
        
        # 文件类型
        if '.m3u8' in url:
            print(f"  文件类型: M3U8 (HLS流媒体)")
        elif '.mp4' in url:
            print(f"  文件类型: MP4 (视频文件)")
        elif '.flv' in url:
            print(f"  文件类型: FLV (Flash视频)")
        else:
            print(f"  文件类型: 未知")
        
        # 是否需要代理
        if '127.0.0.1' in url or 'localhost' in url:
            print(f"  代理状态: 本地代理链接")
        else:
            print(f"  代理状态: 直接链接")
        
    except Exception as e:
        print(f"❌ URL格式分析失败: {str(e)}")

def compare_with_unity_format(nivod_url):
    """对比统一影视的链接格式"""
    try:
        print(f"链接格式对比:")
        
        # 统一影视的典型链接格式（基于之前的分析）
        unity_example = "https://example.com/path/video.m3u8"
        
        print(f"  统一影视格式: 直接视频链接，无代理")
        print(f"  泥视频格式: 直接视频链接，无代理")
        
        # 分析相似性
        if not ('127.0.0.1' in nivod_url or 'localhost' in nivod_url):
            print(f"  ✅ 格式相似: 都是直接可播放的视频链接")
            print(f"  ✅ 兼容性: 应该与统一影视具有相同的TVBox兼容性")
        else:
            print(f"  ❌ 格式不同: 泥视频使用代理，统一影视使用直接链接")
        
    except Exception as e:
        print(f"❌ 格式对比失败: {str(e)}")

def analyze_tvbox_compatibility(player_result):
    """分析TVBox兼容性"""
    try:
        print(f"TVBox兼容性分析:")
        
        # 检查返回格式
        required_fields = ['parse', 'playUrl', 'url', 'header']
        format_score = 0
        
        for field in required_fields:
            if field in player_result:
                print(f"  ✅ 包含必需字段: {field}")
                format_score += 1
            else:
                print(f"  ❌ 缺少必需字段: {field}")
        
        # 检查字段值
        parse_value = player_result.get('parse', -1)
        play_url_value = player_result.get('playUrl', None)
        header_value = player_result.get('header', None)
        url_value = player_result.get('url', '')
        
        if parse_value == 0:
            print(f"  ✅ parse=0 (直接播放)")
            format_score += 1
        else:
            print(f"  ❌ parse={parse_value} (应该为0)")
        
        if play_url_value == '':
            print(f"  ✅ playUrl为空字符串")
            format_score += 1
        else:
            print(f"  ❌ playUrl='{play_url_value}' (应该为空字符串)")
        
        if header_value == {}:
            print(f"  ✅ header为空字典")
            format_score += 1
        else:
            print(f"  ❌ header={header_value} (应该为空字典)")
        
        if url_value and url_value.startswith('http'):
            print(f"  ✅ url为有效的HTTP链接")
            format_score += 1
        else:
            print(f"  ❌ url无效: {url_value}")
        
        # 总体评分
        total_checks = 7
        compatibility_score = (format_score / total_checks) * 100
        
        print(f"\n🎯 TVBox兼容性评分: {compatibility_score:.1f}% ({format_score}/{total_checks})")
        
        if compatibility_score >= 90:
            print(f"  ✅ 兼容性: 优秀 - 应该完全兼容TVBox")
        elif compatibility_score >= 70:
            print(f"  ⚠️ 兼容性: 良好 - 基本兼容TVBox")
        else:
            print(f"  ❌ 兼容性: 较差 - 可能存在TVBox兼容性问题")
        
        # 关键发现
        print(f"\n🔍 关键发现:")
        if not ('127.0.0.1' in url_value or 'localhost' in url_value):
            print(f"  ✅ 返回直接视频链接，与统一影视模式相同")
            print(f"  ✅ 无需localProxy处理，简化了播放流程")
            print(f"  ✅ 这种模式在TVBox中应该工作正常")
        else:
            print(f"  ⚠️ 返回代理链接，需要localProxy处理")
        
    except Exception as e:
        print(f"❌ TVBox兼容性分析失败: {str(e)}")

if __name__ == '__main__':
    test_direct_playback()
