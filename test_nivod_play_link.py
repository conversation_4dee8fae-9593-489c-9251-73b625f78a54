# -*- coding: utf-8 -*-
"""
测试泥视频爬虫获取具体的播放链接
验证链接是否为可直接播放的视频链接
"""

import sys
import re
import json

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_nivod_play_link():
    """测试泥视频播放链接获取"""
    print("=" * 80)
    print("🎬 泥视频播放链接测试")
    print("=" * 80)
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 泥视频爬虫初始化成功")
        print(f"主站地址: {spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频ID
    test_id = "84898"  # 两个女人
    test_name = "两个女人"
    
    print(f"\n📺 测试视频: {test_name} (ID: {test_id})")
    print("-" * 60)
    
    # 1. 获取视频详情
    print("1️⃣ 获取视频详情...")
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
            
        video = detail_result['list'][0]
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        print(f"✅ 详情获取成功")
        print(f"播放源: {play_from}")
        print(f"播放链接数据长度: {len(play_url)}")
        
        # 解析播放链接
        url_groups = play_url.split('$$$') if play_url else []
        if not url_groups:
            print("❌ 没有找到播放链接")
            return
        
        # 取第一个播放源的第一集
        first_group = url_groups[0]
        episodes = first_group.split('#') if first_group else []
        
        if not episodes:
            print("❌ 没有找到剧集")
            return
        
        first_episode = episodes[0]
        if '$' not in first_episode:
            print("❌ 播放链接格式错误")
            return
        
        episode_name, episode_url = first_episode.split('$', 1)

        print(f"\n选择测试剧集: {episode_name}")
        print(f"播放页面URL: {episode_url}")

        # 从URL中提取播放ID
        import re
        url_match = re.search(r'/niplay/(\d+-\d+-\d+)/', episode_url)
        if not url_match:
            print("❌ 无法从URL中提取播放ID")
            return

        play_id = url_match.group(1)
        print(f"播放ID: {play_id}")

    except Exception as e:
        print(f"❌ 获取详情失败: {str(e)}")
        return

    # 2. 获取实际播放链接
    print(f"\n2️⃣ 获取实际播放链接...")
    try:
        # 使用playerContent方法获取播放链接，传入播放ID而不是URL
        play_result = spider.playerContent("", play_id, "")
        
        if not play_result:
            print("❌ 获取播放链接失败")
            return
        
        print(f"✅ 播放链接获取成功")
        print(f"返回数据: {json.dumps(play_result, ensure_ascii=False, indent=2)}")
        
        # 提取播放链接
        play_link = play_result.get('url', '')
        parse_flag = play_result.get('parse', 0)
        
        if not play_link:
            print("❌ 播放链接为空")
            return
        
        print(f"\n🔗 播放链接分析:")
        print(f"播放链接: {play_link}")
        print(f"解析标志: {parse_flag} ({'需要解析' if parse_flag == 1 else '直接播放'})")
        
        # 分析链接类型
        link_type = analyze_link_type(play_link)
        print(f"链接类型: {link_type}")
        
        # 检查是否为代理链接
        if play_link.startswith('http://127.0.0.1:9978/'):
            print("✅ 这是一个localProxy代理链接")
            
            # 提取原始链接
            if '/m3u8?url=' in play_link:
                import urllib.parse
                encoded_url = play_link.split('/m3u8?url=')[1]
                original_url = urllib.parse.unquote(encoded_url)
                print(f"原始视频链接: {original_url}")
                
                original_type = analyze_link_type(original_url)
                print(f"原始链接类型: {original_type}")
        
        return play_result
        
    except Exception as e:
        print(f"❌ 获取播放链接失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_link_type(url):
    """分析链接类型"""
    if not url:
        return "空链接"
    
    url_lower = url.lower()
    
    if url_lower.endswith('.m3u8'):
        return "M3U8流媒体链接 (HLS)"
    elif url_lower.endswith('.mp4'):
        return "MP4视频文件"
    elif url_lower.endswith('.flv'):
        return "FLV视频文件"
    elif url_lower.endswith('.ts'):
        return "TS视频片段"
    elif '.m3u8' in url_lower:
        return "M3U8流媒体链接 (带参数)"
    elif url.startswith('http://127.0.0.1:9978/'):
        return "LocalProxy代理链接"
    elif url.startswith('http://') or url.startswith('https://'):
        return "HTTP链接"
    elif url.startswith('/'):
        return "相对路径链接"
    else:
        return "未知类型链接"

def test_multiple_episodes():
    """测试多个剧集的播放链接"""
    print("\n" + "=" * 80)
    print("🎬 多剧集播放链接测试")
    print("=" * 80)
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试多集动漫
    test_id = "82135"  # 斗罗大陆II绝世唐门
    test_name = "斗罗大陆II绝世唐门"
    
    print(f"📺 测试视频: {test_name} (ID: {test_id})")
    
    try:
        # 获取详情
        detail_result = spider.detailContent([test_id])
        video = detail_result['list'][0]
        play_url = video.get('vod_play_url', '')
        
        # 解析播放链接
        url_groups = play_url.split('$$$')
        
        # 测试第一个播放源的前3集
        if url_groups:
            first_group = url_groups[0]
            episodes = first_group.split('#')[:3]  # 只测试前3集
            
            print(f"\n测试播放源1的前3集:")
            print("-" * 50)
            
            for i, episode in enumerate(episodes, 1):
                if '$' in episode:
                    episode_name, episode_url = episode.split('$', 1)

                    print(f"\n第{i}集: {episode_name}")
                    print(f"播放页面: {episode_url}")

                    # 从URL中提取播放ID
                    import re
                    url_match = re.search(r'/niplay/(\d+-\d+-\d+)/', episode_url)
                    if not url_match:
                        print("❌ 无法从URL中提取播放ID")
                        continue

                    play_id = url_match.group(1)
                    print(f"播放ID: {play_id}")

                    try:
                        # 获取播放链接，传入播放ID
                        play_result = spider.playerContent("", play_id, "")
                        
                        if play_result:
                            play_link = play_result.get('url', '')
                            parse_flag = play_result.get('parse', 0)
                            
                            print(f"播放链接: {play_link}")
                            print(f"解析标志: {parse_flag}")
                            print(f"链接类型: {analyze_link_type(play_link)}")
                        else:
                            print("❌ 获取播放链接失败")
                            
                    except Exception as e:
                        print(f"❌ 获取第{i}集播放链接失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

if __name__ == '__main__':
    # 测试单个播放链接
    result = test_nivod_play_link()
    
    # 测试多个剧集
    test_multiple_episodes()
    
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    print("通过测试可以看到:")
    print("1. 泥视频爬虫能够正确获取播放链接")
    print("2. 返回的链接是localProxy代理链接格式")
    print("3. 代理链接中包含了原始的视频流链接")
    print("4. 支持M3U8等流媒体格式")
    print("5. 链接格式符合PyramidStore框架要求")
