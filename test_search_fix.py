# -*- coding: utf-8 -*-
"""
测试搜索功能修复效果
"""

import sys
import json
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_search_fix():
    """测试搜索功能修复效果"""
    print("=" * 120)
    print("🔧 搜索功能TVBox兼容性修复验证")
    print("=" * 120)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入修复后的泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        from 统一影视 import Spider as UnitySpider
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        unity_spider = UnitySpider()
        unity_spider.init()
        
        print("✅ 爬虫初始化成功")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试关键词
    test_keywords = ["斗罗", "海贼王", "火影"]
    
    for keyword in test_keywords:
        print(f"\n🔍 测试关键词: {keyword}")
        print("-" * 80)
        
        # 1. 测试修复后的搜索结果格式
        print("1️⃣ 测试修复后的搜索结果格式:")
        print("-" * 60)
        test_fixed_search_format(nivod_spider, unity_spider, keyword)
        
        # 2. 验证TVBox兼容性
        print("\n2️⃣ 验证TVBox兼容性:")
        print("-" * 60)
        verify_tvbox_compatibility(nivod_spider, keyword)
        
        print("\n" + "="*80)

def test_fixed_search_format(nivod_spider, unity_spider, keyword):
    """测试修复后的搜索结果格式"""
    try:
        # 获取修复后的泥视频搜索结果
        print(f"获取修复后的泥视频搜索结果...")
        nivod_result = nivod_spider.searchContent(keyword, False, "1")
        
        # 获取统一影视搜索结果作为参考
        print(f"获取统一影视搜索结果作为参考...")
        unity_result = unity_spider.searchContent(keyword, False, "1")
        
        print(f"\n修复后格式对比:")
        print(f"  泥视频结果数量: {len(nivod_result.get('list', []))}")
        print(f"  统一影视结果数量: {len(unity_result.get('list', []))}")
        
        # 分析修复后的字段
        if nivod_result.get('list'):
            nivod_first = nivod_result['list'][0]
            print(f"\n  修复后泥视频字段:")
            for key, value in nivod_first.items():
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {key}: {type(value).__name__} = '{value_str}'")
        
        if unity_result.get('list'):
            unity_first = unity_result['list'][0]
            print(f"\n  统一影视字段（参考）:")
            for key, value in unity_first.items():
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {key}: {type(value).__name__} = '{value_str}'")
        
        # 检查字段完整性
        if nivod_result.get('list') and unity_result.get('list'):
            nivod_keys = set(nivod_result['list'][0].keys())
            unity_keys = set(unity_result['list'][0].keys())
            
            required_fields = ['vod_id', 'vod_name', 'vod_pic', 'vod_remarks', 'vod_year', 'vod_area', 'vod_tag']
            
            print(f"\n  字段完整性检查:")
            missing_fields = []
            for field in required_fields:
                if field in nivod_keys:
                    print(f"    ✅ {field}: 存在")
                else:
                    print(f"    ❌ {field}: 缺失")
                    missing_fields.append(field)
            
            if not missing_fields:
                print(f"  🎯 字段完整性: ✅ 所有必需字段都存在")
            else:
                print(f"  🎯 字段完整性: ❌ 缺少字段: {missing_fields}")
            
            # 检查字段值质量
            print(f"\n  字段值质量检查:")
            nivod_item = nivod_result['list'][0]
            
            quality_issues = []
            for field in required_fields:
                if field in nivod_item:
                    value = nivod_item[field]
                    if not isinstance(value, str):
                        quality_issues.append(f"{field}不是字符串类型")
                    elif not value.strip():
                        quality_issues.append(f"{field}为空")
                    else:
                        print(f"    ✅ {field}: 有效值 '{value}'")
            
            if quality_issues:
                print(f"  🎯 字段值质量: ❌ 问题: {quality_issues}")
            else:
                print(f"  🎯 字段值质量: ✅ 所有字段值都有效")
        
    except Exception as e:
        print(f"❌ 搜索格式测试失败: {str(e)}")

def verify_tvbox_compatibility(nivod_spider, keyword):
    """验证TVBox兼容性"""
    try:
        print(f"TVBox兼容性验证:")
        
        # 获取搜索结果
        search_result = nivod_spider.searchContent(keyword, False, "1")
        
        if not search_result.get('list'):
            print(f"  ❌ 无搜索结果")
            return False
        
        # 检查每个搜索结果的兼容性
        compatibility_score = 0
        total_checks = 0
        
        for i, item in enumerate(search_result['list'][:3]):  # 检查前3个结果
            print(f"\n  检查第{i+1}个结果:")
            item_score = 0
            item_checks = 0
            
            # 必需字段检查
            required_fields = {
                'vod_id': '视频ID',
                'vod_name': '视频名称', 
                'vod_pic': '视频图片',
                'vod_remarks': '备注信息',
                'vod_year': '年份',
                'vod_area': '地区',
                'vod_tag': '类型标签'
            }
            
            for field, desc in required_fields.items():
                item_checks += 1
                if field in item:
                    value = item[field]
                    if isinstance(value, str) and value.strip():
                        print(f"    ✅ {desc}({field}): '{value}'")
                        item_score += 1
                    elif isinstance(value, str):
                        print(f"    ⚠️ {desc}({field}): 空值")
                        item_score += 0.5
                    else:
                        print(f"    ❌ {desc}({field}): 类型错误 {type(value)}")
                else:
                    print(f"    ❌ {desc}({field}): 缺失")
            
            # 数据质量检查
            item_checks += 3
            
            # vod_id格式检查
            vod_id = item.get('vod_id', '')
            if vod_id and vod_id.isdigit():
                print(f"    ✅ vod_id格式: 纯数字 '{vod_id}'")
                item_score += 1
            else:
                print(f"    ❌ vod_id格式: 非纯数字 '{vod_id}'")
            
            # vod_pic URL检查
            vod_pic = item.get('vod_pic', '')
            if vod_pic and vod_pic.startswith('http'):
                print(f"    ✅ vod_pic格式: 有效URL")
                item_score += 1
            else:
                print(f"    ❌ vod_pic格式: 无效URL '{vod_pic[:50]}...'")
            
            # 特殊字符检查
            has_special_chars = False
            for field, value in item.items():
                if isinstance(value, str):
                    if any(char in value for char in ['\n', '\r', '\t']) or any(ord(char) < 32 for char in value if char not in ['\n', '\r', '\t']):
                        has_special_chars = True
                        break
            
            if not has_special_chars:
                print(f"    ✅ 特殊字符检查: 无问题字符")
                item_score += 1
            else:
                print(f"    ❌ 特殊字符检查: 发现问题字符")
            
            item_compatibility = (item_score / item_checks) * 100
            print(f"    🎯 第{i+1}个结果兼容性: {item_compatibility:.1f}% ({item_score}/{item_checks})")
            
            compatibility_score += item_score
            total_checks += item_checks
        
        # 总体兼容性评分
        overall_compatibility = (compatibility_score / total_checks) * 100
        print(f"\n  🎯 总体TVBox兼容性: {overall_compatibility:.1f}% ({compatibility_score}/{total_checks})")
        
        if overall_compatibility >= 95:
            print(f"  ✅ 兼容性等级: 优秀 - 应该完全兼容TVBox，不会闪退")
            return True
        elif overall_compatibility >= 85:
            print(f"  ⚠️ 兼容性等级: 良好 - 基本兼容TVBox，闪退风险较低")
            return True
        elif overall_compatibility >= 70:
            print(f"  ⚠️ 兼容性等级: 一般 - 可能存在兼容性问题")
            return False
        else:
            print(f"  ❌ 兼容性等级: 较差 - 很可能导致TVBox闪退")
            return False
        
    except Exception as e:
        print(f"❌ TVBox兼容性验证失败: {str(e)}")
        return False

if __name__ == '__main__':
    test_search_fix()
