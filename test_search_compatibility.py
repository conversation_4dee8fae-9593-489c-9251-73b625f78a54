# -*- coding: utf-8 -*-
"""
搜索功能TVBox兼容性对比分析
"""

import sys
import json
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_search_compatibility():
    """测试搜索功能TVBox兼容性"""
    print("=" * 120)
    print("🔍 搜索功能TVBox兼容性对比分析")
    print("=" * 120)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入两个爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        from 统一影视 import Spider as UnitySpider
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        unity_spider = UnitySpider()
        unity_spider.init()
        
        print("✅ 两个爬虫初始化成功")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试关键词
    test_keyword = "斗罗"
    
    print(f"\n🔍 测试关键词: {test_keyword}")
    print("-" * 80)
    
    # 1. 对比搜索结果数据结构
    print("1️⃣ 对比搜索结果数据结构:")
    print("-" * 60)
    compare_search_results(nivod_spider, unity_spider, test_keyword)
    
    # 2. 检查数据类型和格式
    print("\n2️⃣ 检查数据类型和格式:")
    print("-" * 60)
    check_data_types(nivod_spider, unity_spider, test_keyword)
    
    # 3. 验证vod_id格式一致性
    print("\n3️⃣ 验证vod_id格式一致性:")
    print("-" * 60)
    verify_vod_id_consistency(nivod_spider, test_keyword)
    
    # 4. 检查特殊字符和编码问题
    print("\n4️⃣ 检查特殊字符和编码问题:")
    print("-" * 60)
    check_encoding_issues(nivod_spider, test_keyword)
    
    # 5. 模拟TVBox数据流转换
    print("\n5️⃣ 模拟TVBox数据流转换:")
    print("-" * 60)
    simulate_tvbox_flow(nivod_spider, test_keyword)

def compare_search_results(nivod_spider, unity_spider, keyword):
    """对比搜索结果数据结构"""
    try:
        # 获取泥视频搜索结果
        print(f"获取泥视频搜索结果...")
        nivod_result = nivod_spider.searchContent(keyword, False, "1")
        
        # 获取统一影视搜索结果
        print(f"获取统一影视搜索结果...")
        unity_result = unity_spider.searchContent(keyword, False, "1")
        
        print(f"\n数据结构对比:")
        print(f"  泥视频结果数量: {len(nivod_result.get('list', []))}")
        print(f"  统一影视结果数量: {len(unity_result.get('list', []))}")
        
        # 分析第一个结果的字段
        if nivod_result.get('list'):
            nivod_first = nivod_result['list'][0]
            print(f"\n  泥视频第一个结果字段:")
            for key, value in nivod_first.items():
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {key}: {type(value).__name__} = '{value_str}'")
        
        if unity_result.get('list'):
            unity_first = unity_result['list'][0]
            print(f"\n  统一影视第一个结果字段:")
            for key, value in unity_first.items():
                value_str = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                print(f"    {key}: {type(value).__name__} = '{value_str}'")
        
        # 检查字段一致性
        if nivod_result.get('list') and unity_result.get('list'):
            nivod_keys = set(nivod_result['list'][0].keys())
            unity_keys = set(unity_result['list'][0].keys())
            
            common_keys = nivod_keys & unity_keys
            nivod_only = nivod_keys - unity_keys
            unity_only = unity_keys - nivod_keys
            
            print(f"\n  字段对比:")
            print(f"    共同字段: {list(common_keys)}")
            if nivod_only:
                print(f"    泥视频独有: {list(nivod_only)}")
            if unity_only:
                print(f"    统一影视独有: {list(unity_only)}")
        
        return nivod_result, unity_result
        
    except Exception as e:
        print(f"❌ 搜索结果对比失败: {str(e)}")
        return None, None

def check_data_types(nivod_spider, unity_spider, keyword):
    """检查数据类型和格式"""
    try:
        nivod_result = nivod_spider.searchContent(keyword, False, "1")
        unity_result = unity_spider.searchContent(keyword, False, "1")
        
        print(f"数据类型检查:")
        
        # 检查泥视频数据类型
        if nivod_result.get('list'):
            nivod_item = nivod_result['list'][0]
            print(f"\n  泥视频数据类型:")
            
            # 检查关键字段
            critical_fields = ['vod_id', 'vod_name', 'vod_pic', 'vod_remarks']
            for field in critical_fields:
                if field in nivod_item:
                    value = nivod_item[field]
                    value_type = type(value).__name__
                    
                    # 检查是否为字符串
                    if not isinstance(value, str):
                        print(f"    ❌ {field}: {value_type} (应该是str) = {value}")
                    else:
                        print(f"    ✅ {field}: {value_type} = '{value[:30]}{'...' if len(str(value)) > 30 else ''}'")
                        
                        # 检查字符串是否为空或只包含空白字符
                        if not value.strip():
                            print(f"      ⚠️ {field}为空或只包含空白字符")
                        
                        # 检查特殊字符
                        if any(char in value for char in ['\n', '\r', '\t']):
                            print(f"      ⚠️ {field}包含换行符或制表符")
                else:
                    print(f"    ❌ 缺少字段: {field}")
        
        # 检查统一影视数据类型（作为参考）
        if unity_result.get('list'):
            unity_item = unity_result['list'][0]
            print(f"\n  统一影视数据类型（参考）:")
            
            critical_fields = ['vod_id', 'vod_name', 'vod_pic', 'vod_remarks']
            for field in critical_fields:
                if field in unity_item:
                    value = unity_item[field]
                    value_type = type(value).__name__
                    print(f"    ✅ {field}: {value_type} = '{str(value)[:30]}{'...' if len(str(value)) > 30 else ''}'")
        
    except Exception as e:
        print(f"❌ 数据类型检查失败: {str(e)}")

def verify_vod_id_consistency(nivod_spider, keyword):
    """验证vod_id格式一致性"""
    try:
        print(f"vod_id格式一致性验证:")
        
        # 获取搜索结果
        search_result = nivod_spider.searchContent(keyword, False, "1")
        
        if not search_result.get('list'):
            print(f"  ❌ 无搜索结果")
            return
        
        # 取第一个结果测试
        first_item = search_result['list'][0]
        vod_id = first_item.get('vod_id')
        
        if not vod_id:
            print(f"  ❌ vod_id为空")
            return
        
        print(f"  搜索结果vod_id: {vod_id} (类型: {type(vod_id).__name__})")
        
        # 测试是否能传递给detailContent
        print(f"  测试detailContent调用...")
        detail_result = nivod_spider.detailContent([vod_id])
        
        if detail_result and detail_result.get('list'):
            detail_item = detail_result['list'][0]
            detail_vod_id = detail_item.get('vod_id')
            
            print(f"  详情页vod_id: {detail_vod_id} (类型: {type(detail_vod_id).__name__})")
            
            # 检查ID一致性
            if str(vod_id) == str(detail_vod_id):
                print(f"  ✅ vod_id格式一致")
            else:
                print(f"  ❌ vod_id格式不一致: 搜索={vod_id}, 详情={detail_vod_id}")
        else:
            print(f"  ❌ detailContent调用失败")
        
    except Exception as e:
        print(f"❌ vod_id一致性验证失败: {str(e)}")

def check_encoding_issues(nivod_spider, keyword):
    """检查特殊字符和编码问题"""
    try:
        print(f"特殊字符和编码检查:")
        
        search_result = nivod_spider.searchContent(keyword, False, "1")
        
        if not search_result.get('list'):
            print(f"  ❌ 无搜索结果")
            return
        
        issues_found = []
        
        for i, item in enumerate(search_result['list'][:3]):  # 检查前3个结果
            print(f"\n  检查第{i+1}个结果:")
            
            for field, value in item.items():
                if isinstance(value, str):
                    # 检查各种可能导致TVBox崩溃的问题
                    
                    # 1. 检查非打印字符
                    non_printable = [char for char in value if ord(char) < 32 and char not in ['\n', '\r', '\t']]
                    if non_printable:
                        issues_found.append(f"{field}包含非打印字符: {[ord(c) for c in non_printable]}")
                    
                    # 2. 检查特殊Unicode字符
                    try:
                        value.encode('utf-8')
                    except UnicodeEncodeError as e:
                        issues_found.append(f"{field}包含无效Unicode字符: {str(e)}")
                    
                    # 3. 检查HTML实体
                    if '&' in value and ';' in value:
                        import re
                        html_entities = re.findall(r'&[a-zA-Z0-9#]+;', value)
                        if html_entities:
                            issues_found.append(f"{field}包含HTML实体: {html_entities}")
                    
                    # 4. 检查过长字符串
                    if len(value) > 1000:
                        issues_found.append(f"{field}字符串过长: {len(value)}字符")
                    
                    # 5. 检查空值问题
                    if field in ['vod_id', 'vod_name'] and not value.strip():
                        issues_found.append(f"关键字段{field}为空")
                    
                    print(f"    {field}: 长度={len(value)}, 编码=OK")
        
        if issues_found:
            print(f"\n  ❌ 发现问题:")
            for issue in issues_found:
                print(f"    - {issue}")
        else:
            print(f"\n  ✅ 未发现编码或特殊字符问题")
        
    except Exception as e:
        print(f"❌ 编码检查失败: {str(e)}")

def simulate_tvbox_flow(nivod_spider, keyword):
    """模拟TVBox数据流转换"""
    try:
        print(f"模拟TVBox数据流转换:")
        
        # 步骤1：搜索
        print(f"  步骤1: 执行搜索")
        search_result = nivod_spider.searchContent(keyword, False, "1")
        
        if not search_result.get('list'):
            print(f"    ❌ 搜索失败")
            return
        
        print(f"    ✅ 搜索成功，结果数: {len(search_result['list'])}")
        
        # 步骤2：选择第一个结果
        first_item = search_result['list'][0]
        vod_id = first_item.get('vod_id')
        vod_name = first_item.get('vod_name')
        
        print(f"  步骤2: 选择搜索结果")
        print(f"    选中: {vod_name} (ID: {vod_id})")
        
        # 步骤3：获取详情（模拟TVBox点击跳转）
        print(f"  步骤3: 获取详情页面")
        try:
            detail_result = nivod_spider.detailContent([vod_id])
            
            if detail_result and detail_result.get('list'):
                detail_item = detail_result['list'][0]
                print(f"    ✅ 详情获取成功")
                print(f"    标题: {detail_item.get('vod_name', 'N/A')}")
                print(f"    播放源数: {len(detail_item.get('vod_play_from', '').split('$$$')) if detail_item.get('vod_play_from') else 0}")
                
                # 步骤4：检查播放链接格式
                play_url = detail_item.get('vod_play_url', '')
                if play_url:
                    print(f"    ✅ 播放链接存在")
                    
                    # 解析第一个播放链接
                    url_groups = play_url.split('$$$')
                    if url_groups:
                        first_group = url_groups[0]
                        episodes = first_group.split('#')
                        if episodes and '$' in episodes[0]:
                            ep_name, ep_url = episodes[0].split('$', 1)
                            print(f"    ✅ 播放链接格式正确: {ep_name} -> {ep_url}")
                            
                            # 步骤5：测试播放（模拟TVBox播放）
                            print(f"  步骤5: 测试播放链接")
                            import re
                            play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                            if play_match:
                                play_id = play_match.group(1)
                                player_result = nivod_spider.playerContent("", play_id, "")
                                
                                if player_result and player_result.get('url'):
                                    print(f"    ✅ 播放链接获取成功")
                                    print(f"    🎯 完整流程验证: ✅ 成功")
                                    return True
                                else:
                                    print(f"    ❌ 播放链接获取失败")
                            else:
                                print(f"    ❌ 播放ID解析失败")
                        else:
                            print(f"    ❌ 播放链接格式错误")
                    else:
                        print(f"    ❌ 播放链接为空")
                else:
                    print(f"    ❌ 无播放链接")
            else:
                print(f"    ❌ 详情获取失败")
                print(f"    🎯 完整流程验证: ❌ 在详情页面失败")
                return False
                
        except Exception as e:
            print(f"    ❌ 详情获取异常: {str(e)}")
            print(f"    🎯 完整流程验证: ❌ 异常导致失败")
            return False
        
        print(f"    🎯 完整流程验证: ❌ 播放环节失败")
        return False
        
    except Exception as e:
        print(f"❌ TVBox流程模拟失败: {str(e)}")
        return False

if __name__ == '__main__':
    test_search_compatibility()
