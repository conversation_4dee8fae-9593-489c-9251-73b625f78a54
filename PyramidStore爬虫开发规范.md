# PyramidStore爬虫开发规范

## 📋 总则

本规范基于对PyramidStore项目的深入分析，结合spider.md文档、开发记录文档和61个现有爬虫实现的最佳实践，建立了严格的开发标准。**所有后续爬虫开发必须严格遵循此规范。**

## 🚫 严格约束条件

### 1. 模块使用约束
- **只能使用** `模块清单.md` 中列出的模块
- **禁止引入** 任何新的第三方依赖包
- **标准库模块**: 18个已批准模块（re, json, time, os, sys等）
- **第三方依赖**: 5个核心包（requests, lxml, pyquery, pycryptodome, aiohttp）

### 2. 架构约束
- **必须继承** `base.spider.Spider` 基类
- **必须实现** 所有抽象方法
- **禁止修改** 基类接口
- **文件位置** 必须在 `plugin/` 目录下

### 3. 数据格式约束
- **严格遵循** TVBox标准格式
- **统一返回** 规定的JSON结构
- **字段命名** 必须使用标准字段名

## 🏗️ 标准类结构

### 基础模板
```python
# -*- coding: utf-8 -*-
import sys
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def getName(self):
        return "爬虫名称"
    
    def init(self, extend=""):
        """初始化方法 - 设置host和必要参数"""
        self.host = "https://example.com"
        pass
    
    def isVideoFormat(self, url):
        pass
    
    def manualVideoCheck(self):
        pass
    
    def destroy(self):
        pass
    
    # 核心方法实现...
```

### 必须实现的抽象方法

#### 1. homeContent(self, filter)
```python
def homeContent(self, filter):
    """首页内容和分类定义"""
    result = {}
    
    # 分类定义（必需）
    classes = [
        {'type_name': '电影', 'type_id': '1'},
        {'type_name': '电视剧', 'type_id': '2'},
        # ...更多分类
    ]
    result['class'] = classes
    
    # 筛选配置（可选）
    result['filters'] = self._get_filters()
    
    # 推荐视频（可选）
    result['list'] = self._get_home_videos()
    
    return result
```

#### 2. categoryContent(self, tid, pg, filter, extend)
```python
def categoryContent(self, tid, pg, filter, extend):
    """分类内容"""
    try:
        # 构建请求参数
        params = {
            'tid': tid,
            'pg': pg,
            'area': extend.get('area', ''),
            'year': extend.get('year', ''),
            # ...其他筛选参数
        }
        
        # 获取数据并解析
        videos = self._get_category_videos(params)
        
        return {
            'list': videos,
            'page': int(pg),
            'pagecount': 999,  # 总页数
            'limit': 20,       # 每页数量
            'total': 999       # 总数量
        }
    except Exception as e:
        self.log(f"分类内容获取出错: {str(e)}")
        return {'list': []}
```

#### 3. detailContent(self, ids)
```python
def detailContent(self, ids):
    """详情页面"""
    try:
        vod_id = ids[0]
        
        # 获取详情信息
        vod_info = self._get_detail_info(vod_id)
        
        # 获取播放源
        play_from, play_url = self._get_play_sources(vod_id)
        
        vod_info.update({
            'vod_play_from': '$$$'.join(play_from),
            'vod_play_url': '$$$'.join(play_url)
        })
        
        return {'list': [vod_info]}
    except Exception as e:
        self.log(f"详情获取出错: {str(e)}")
        return {'list': []}
```

#### 4. searchContent(self, key, quick, pg='1')
```python
def searchContent(self, key, quick, pg='1'):
    """搜索功能"""
    try:
        # 构建搜索参数
        search_params = {
            'keyword': key,
            'page': pg
        }
        
        # 获取搜索结果
        videos = self._search_videos(search_params)
        
        # 智能过滤（提高搜索精度）
        filtered_videos = self._filter_search_results(videos, key)
        
        return {
            'list': filtered_videos,
            'page': int(pg)
        }
    except Exception as e:
        self.log(f"搜索出错: {str(e)}")
        return {'list': []}
```

#### 5. playerContent(self, flag, id, vipFlags)
```python
def playerContent(self, flag, id, vipFlags):
    """播放链接"""
    try:
        # 解析播放链接
        play_url = self._parse_play_url(flag, id)
        
        return {
            'parse': 0,  # 0=直链 1=需要解析
            'url': play_url,
            'header': self._get_play_headers()
        }
    except Exception as e:
        self.log(f"播放链接获取出错: {str(e)}")
        return {'parse': 1, 'url': ''}
```

## 📊 标准数据格式

### 视频对象格式
```python
{
    'vod_id': '视频唯一ID',
    'vod_name': '视频标题',
    'vod_pic': '图片URL（完整HTTP地址）',
    'vod_remarks': '备注信息（如更新状态）',
    'vod_year': '年份',
    'vod_area': '地区',
    'vod_director': '导演',
    'vod_actor': '演员',
    'vod_content': '简介',
    'vod_play_from': '播放源1$$$播放源2',
    'vod_play_url': '第1集$链接1#第2集$链接2$$$第1集$链接1#第2集$链接2'
}
```

### 分类对象格式
```python
{
    'type_name': '分类显示名称',
    'type_id': '分类唯一标识'
}
```

### 筛选配置格式
```python
{
    'type_id': [
        {
            'key': 'area',
            'name': '地区',
            'value': [
                {'n': '全部', 'v': ''},
                {'n': '中国大陆', 'v': '中国大陆'},
                # ...更多选项
            ]
        }
    ]
}
```

## 🎯 爬虫类型和实现模式

### APP类型爬虫
**特点**: 调用移动应用API接口
**实现模式**:
- 设置专用headers（User-Agent等）
- 处理加密解密（如需要）
- 直接返回API数据
- 参考: `美帕APP.py`, `皮皮虾APP.py`

### HTML类型爬虫  
**特点**: 解析网页HTML内容
**实现模式**:
- 使用XPath选择器
- 处理懒加载图片
- 智能去重机制
- 参考: `统一影视.py`, `播剧网_优化版.py`

### Official类型爬虫
**特点**: 官方平台API
**实现模式**:
- 标准API调用
- 官方数据格式转换
- 高稳定性要求

## 🚀 性能标准

### 响应时间要求
- **目标标准**: 所有方法 < 5秒
- **优秀标准**: 所有方法 < 3秒  
- **卓越标准**: 所有方法 < 1秒

### 代码质量要求
- **代码行数**: 建议 < 500行
- **模块化程度**: 通用方法提取率 > 60%
- **注释覆盖**: 关键逻辑100%注释
- **错误处理**: 网络请求100%异常处理

## 💡 最佳实践

### 1. 开发流程
1. **网站分析**: 使用web-fetch工具全面分析
2. **架构设计**: 确定爬虫类型和技术方案
3. **基础开发**: 实现核心功能
4. **测试验证**: 功能和性能测试
5. **优化完善**: 代码精简和性能优化

### 2. 代码优化
- **通用方法提取**: 避免重复代码
- **智能缓存**: 减少重复请求
- **选择器优化**: 使用高效XPath
- **异常处理**: 完善的错误处理机制

### 3. 数据处理
- **智能去重**: 使用set()确保数据唯一性
- **图片处理**: 正确处理懒加载占位符
- **链接处理**: 确保完整的HTTP地址
- **编码处理**: 正确处理中文编码

### 4. 播放源优化
- **优先级排序**: 高质量播放源优先
- **唯一性保证**: 避免播放源名称重复
- **SID标识**: 确保播放时正确识别

## 🔧 技术要点

### 懒加载图片处理
```python
def _extract_image(self, element):
    """处理懒加载图片"""
    pic_selectors = [
        './/img/@data-original',
        './/img/@data-src', 
        './/img/@src'
    ]
    
    for selector in pic_selectors:
        pics = element.xpath(selector)
        for p in pics:
            if (p and not p.endswith('blank.gif') and 
                not p.startswith('data:image/')):
                return self._format_url(p)
    return ''
```

### 智能搜索过滤
```python
def _filter_search_results(self, videos, search_key):
    """智能过滤搜索结果"""
    filtered = []
    search_key_lower = search_key.lower()
    
    for video in videos:
        title_lower = video['vod_name'].lower()
        if search_key_lower in title_lower:
            filtered.append(video)
    
    return filtered
```

### 播放源排序
```python
def _sort_play_sources(self, sources):
    """播放源智能排序"""
    priority_map = {
        '1080': 1,
        '超清': 2, 
        '蓝光': 3,
        '高清': 4,
        '标清': 5
    }
    
    return sorted(sources, key=lambda x: priority_map.get(x['name'], 999))
```

## 📋 测试验证标准

### 功能测试
- ✅ 首页内容: 获取分类和推荐视频
- ✅ 分类浏览: 各分类正常显示
- ✅ 搜索功能: 关键词搜索准确
- ✅ 详情页面: 信息完整，播放源正常
- ✅ 播放链接: 链接有效，格式正确

### 性能测试
- ⏱️ 响应时间: 记录各方法耗时
- 📊 数据量: 验证返回数据数量
- 🔄 稳定性: 多次调用测试
- 💾 内存使用: 监控内存占用

### 兼容性测试
- 📱 TVBox兼容: 100%兼容TVBox标准
- 🔧 配置正确: JSON配置格式正确
- 🎮 播放正常: 播放链接在TVBox中正常工作

## ⚠️ 常见问题和解决方案

### 1. 图片不显示
**原因**: 懒加载占位符或相对路径
**解决**: 正确处理data-src属性，转换为完整URL

### 2. 搜索结果不准确
**原因**: 未过滤无关结果
**解决**: 实现智能搜索过滤算法

### 3. 播放源重复
**原因**: 未去重或命名冲突
**解决**: 使用SID标识和智能重命名

### 4. 性能过慢
**原因**: 重复请求或低效选择器
**解决**: 优化XPath，减少网络请求

## 🎯 质量保证流程

1. **代码审查**: 检查是否符合规范
2. **功能测试**: 验证所有功能正常
3. **性能测试**: 确保响应时间达标
4. **兼容性测试**: 验证TVBox兼容性
5. **文档更新**: 更新开发记录

## 📚 开发示例

### 完整爬虫示例
```python
# -*- coding: utf-8 -*-
import sys
sys.path.append('..')
from base.spider import Spider

class Spider(Spider):
    def getName(self):
        return "示例爬虫"

    def init(self, extend=""):
        self.host = "https://example.com"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

    def homeContent(self, filter):
        result = {}

        # 分类定义
        classes = [
            {'type_name': '电影', 'type_id': '1'},
            {'type_name': '电视剧', 'type_id': '2'},
            {'type_name': '动漫', 'type_id': '3'},
        ]
        result['class'] = classes

        # 获取首页推荐
        try:
            rsp = self.fetch(self.host, headers=self.headers)
            doc = self.html(rsp.text)
            videos = self._extract_videos(doc.xpath('//div[@class="video-item"]'))
            result['list'] = videos
        except Exception as e:
            self.log(f"首页获取出错: {str(e)}")
            result['list'] = []

        return result

    def _extract_videos(self, elements):
        """通用视频信息提取方法"""
        videos = []
        seen_ids = set()

        for element in elements:
            try:
                # 提取基本信息
                links = element.xpath('.//a[contains(@href,"/detail/")]/@href')
                if not links:
                    continue

                vod_id = self.regStr(r'/detail/(\d+)', links[0])
                if vod_id in seen_ids:
                    continue
                seen_ids.add(vod_id)

                title = self._extract_title(element)
                pic = self._extract_image(element)
                remarks = self._extract_remarks(element)

                if title and vod_id:
                    videos.append({
                        'vod_id': vod_id,
                        'vod_name': title,
                        'vod_pic': pic,
                        'vod_remarks': remarks
                    })
            except Exception as e:
                self.log(f"视频信息提取出错: {str(e)}")
                continue

        return videos

    def _extract_title(self, element):
        """提取标题"""
        title_selectors = [
            './/a/@title',
            './/h3/text()',
            './/span[@class="title"]/text()'
        ]

        for selector in title_selectors:
            titles = element.xpath(selector)
            for t in titles:
                if t and t.strip() and len(t.strip()) > 1:
                    return t.strip()
        return ''

    def _extract_image(self, element):
        """提取图片（处理懒加载）"""
        pic_selectors = [
            './/img/@data-original',
            './/img/@data-src',
            './/img/@src'
        ]

        for selector in pic_selectors:
            pics = element.xpath(selector)
            for p in pics:
                if (p and not p.endswith('blank.gif') and
                    not p.startswith('data:image/') and 'base64' not in p):
                    return self._format_url(p)
        return ''

    def _format_url(self, url):
        """格式化URL为完整地址"""
        if url.startswith('//'):
            return 'https:' + url
        elif url.startswith('/'):
            return self.host + url
        elif url.startswith('http'):
            return url
        return ''

    # 其他方法实现...
```

## 🔍 调试和测试

### 本地调试
使用 `base/local.py` 进行爬虫调试：
```python
# 在爬虫文件末尾添加测试代码
if __name__ == '__main__':
    spider = Spider()
    spider.init()

    # 测试首页
    print("=== 测试首页 ===")
    home_result = spider.homeContent({})
    print(f"分类数量: {len(home_result.get('class', []))}")
    print(f"推荐视频: {len(home_result.get('list', []))}")

    # 测试搜索
    print("=== 测试搜索 ===")
    search_result = spider.searchContent("测试", False)
    print(f"搜索结果: {len(search_result.get('list', []))}")
```

### 性能测试模板
```python
import time

def test_performance():
    spider = Spider()
    spider.init()

    # 测试各功能性能
    start_time = time.time()
    home_result = spider.homeContent({})
    home_time = time.time() - start_time

    start_time = time.time()
    search_result = spider.searchContent("测试", False)
    search_time = time.time() - start_time

    print(f"首页耗时: {home_time:.2f}秒")
    print(f"搜索耗时: {search_time:.2f}秒")

    # 性能评级
    if home_time < 1 and search_time < 1:
        print("性能评级: 🚀 卓越")
    elif home_time < 3 and search_time < 3:
        print("性能评级: ⭐ 优秀")
    elif home_time < 5 and search_time < 5:
        print("性能评级: ✅ 达标")
    else:
        print("性能评级: ❌ 需要优化")
```

## 📖 配置文件示例

### TVBox配置
```json
{
    "key": "示例爬虫",
    "name": "示例爬虫",
    "type": 3,
    "api": "爬虫所在位置/示例爬虫.py",
    "searchable": 1,
    "quickSearch": 1,
    "filterable": 1,
    "changeable": 0,
    "ext": {
        "site": "https://example.com"
    }
}
```

### 扩展配置说明
- `searchable`: 是否支持搜索 (0/1)
- `quickSearch`: 是否支持快速搜索 (0/1)
- `filterable`: 是否支持筛选 (0/1)
- `changeable`: 是否支持换源 (0/1)
- `ext`: 扩展配置，传递给init方法

## 🎓 学习资源

### 参考案例
1. **APP类型**: `美帕APP.py` - 简单API调用模式
2. **HTML类型**: `播剧网_优化版.py` - 完整HTML解析
3. **复杂加密**: `皮皮虾APP.py` - AES加密解密
4. **高性能**: `统一影视_精简版.py` - 性能优化典范

### 开发记录文档
- `播剧网爬虫开发完整记忆.md` - 完整开发流程
- `统一影视爬虫_项目总结.md` - 性能优化经验
- `两个BT爬虫开发完整记录.md` - 高性能开发案例

### 技术文档
- `spider.md` - 官方开发指南
- `模块清单.md` - 允许使用的模块列表
- `开发环境说明.md` - 环境配置指南

---

**本规范为PyramidStore项目的强制性标准，所有开发者必须严格遵循。违反规范的代码将不被接受。**

**最后更新**: 2025年1月
**版本**: v1.0
**状态**: 正式发布
**维护者**: Augment Agent
