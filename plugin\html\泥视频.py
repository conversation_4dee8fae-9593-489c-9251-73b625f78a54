# -*- coding: utf-8 -*-
"""
泥视频爬虫插件
网站: https://www.nivod.vip/
类型: HTML解析型
作者: Augment Agent
创建时间: 2025-01-03
版本: v1.0

严格遵循PyramidStore开发规范
- 只使用模块清单.md中允许的模块
- 继承Spider基类并实现所有抽象方法
- 返回数据格式100%兼容TVBox标准
- 所有方法响应时间<5秒，目标<3秒
"""

import sys
sys.path.append('..')
from base.spider import Spider
import re
import json
import time

class Spider(Spider):
    
    def getName(self):
        return "泥视频"
    
    def init(self, extend=""):
        """初始化方法 - 设置host和必要参数"""
        self.host = "https://www.nivod.vip"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': self.host
        }
        
        # 分类定义
        self.categories = [
            {'type_name': '电影', 'type_id': '1'},
            {'type_name': '剧集', 'type_id': '2'},
            {'type_name': '综艺', 'type_id': '3'},
            {'type_name': '动漫', 'type_id': '4'}
        ]
        
        # 筛选配置
        self.filters = {
            '1': [  # 电影筛选
                {
                    'key': 'area',
                    'name': '地区',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '大陆', 'v': '大陆'},
                        {'n': '香港', 'v': '香港'},
                        {'n': '台湾', 'v': '台湾'},
                        {'n': '美国', 'v': '美国'},
                        {'n': '韩国', 'v': '韩国'},
                        {'n': '日本', 'v': '日本'},
                        {'n': '泰国', 'v': '泰国'},
                        {'n': '英国', 'v': '英国'},
                        {'n': '法国', 'v': '法国'},
                        {'n': '其他', 'v': '其他'}
                    ]
                },
                {
                    'key': 'year',
                    'name': '年份',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '2025', 'v': '2025'},
                        {'n': '2024', 'v': '2024'},
                        {'n': '2023', 'v': '2023'},
                        {'n': '2022', 'v': '2022'},
                        {'n': '2021', 'v': '2021'},
                        {'n': '2020', 'v': '2020'},
                        {'n': '更早', 'v': '更早'}
                    ]
                },
                {
                    'key': 'type',
                    'name': '类型',
                    'value': [
                        {'n': '全部', 'v': ''},
                        {'n': '动作片', 'v': '动作片'},
                        {'n': '喜剧片', 'v': '喜剧片'},
                        {'n': '爱情片', 'v': '爱情片'},
                        {'n': '科幻片', 'v': '科幻片'},
                        {'n': '恐怖片', 'v': '恐怖片'},
                        {'n': '剧情片', 'v': '剧情片'},
                        {'n': '战争片', 'v': '战争片'},
                        {'n': '悬疑片', 'v': '悬疑片'}
                    ]
                }
            ]
        }
        
        # 复制电影筛选到其他分类
        self.filters['2'] = self.filters['1']  # 剧集
        self.filters['3'] = self.filters['1']  # 综艺  
        self.filters['4'] = self.filters['1']  # 动漫
        
        self.log(f"泥视频爬虫初始化完成 - {self.host}")
    
    def isVideoFormat(self, url):
        """检查是否为视频格式"""
        if not url:
            return False
        video_formats = ['.mp4', '.m3u8', '.flv', '.avi', '.mkv', '.ts']
        return any(fmt in url.lower() for fmt in video_formats)

    def manualVideoCheck(self):
        """手动视频检查"""
        return False

    def destroy(self):
        """销毁方法 - 清理资源"""
        pass
    
    def homeContent(self, filter):
        """首页内容和分类定义"""
        try:
            result = {}
            
            # 返回分类定义（必需）
            result['class'] = self.categories
            
            # 返回筛选配置（可选）
            result['filters'] = self.filters
            
            # 获取首页推荐视频（可选）
            try:
                rsp = self.fetch(self.host, headers=self.headers)
                if rsp and rsp.status_code == 200:
                    doc = self.html(rsp.text)
                    videos = self._extract_home_videos(doc)
                    result['list'] = videos[:20]  # 限制数量
                else:
                    result['list'] = []
            except Exception as e:
                self.log(f"获取首页推荐视频出错: {str(e)}")
                result['list'] = []
            
            self.log(f"首页内容获取完成 - 分类数: {len(result['class'])}, 推荐数: {len(result.get('list', []))}")
            return result
            
        except Exception as e:
            self.log(f"首页内容获取出错: {str(e)}")
            return {'class': self.categories, 'filters': self.filters, 'list': []}
    
    def categoryContent(self, tid, pg, filter, extend):
        """分类内容"""
        try:
            # 优先使用简单的分类URL
            if not extend or not any(extend.values()):
                # 使用简单分类URL
                url = f"{self.host}/t/{tid}/"
                if int(pg) > 1:
                    url = f"{self.host}/t/{tid}/page/{pg}/"
            else:
                # 构建筛选URL
                area = extend.get('area', '')
                year = extend.get('year', '')
                type_name = extend.get('type', '')

                # 构建URL: /k/{type_id}-{area}-{type}-{lang}-{year}-{letter}-{order}-{page}---/
                url = f"{self.host}/k/{tid}-{area}-{type_name}----{year}---{pg}---/"

            self.log(f"分类请求: {url}")

            rsp = self.fetch(url, headers=self.headers)
            if not rsp or rsp.status_code != 200:
                return {'list': []}

            doc = self.html(rsp.text)
            videos = self._extract_videos(doc)

            # 获取分页信息
            pagecount = self._get_page_count(doc)

            result = {
                'list': videos,
                'page': int(pg),
                'pagecount': pagecount,
                'limit': 20,
                'total': pagecount * 20
            }

            self.log(f"分类内容获取完成 - 页码: {pg}, 视频数: {len(videos)}")
            return result

        except Exception as e:
            self.log(f"分类内容获取出错: {str(e)}")
            return {'list': []}
    
    def detailContent(self, ids):
        """详情页面"""
        try:
            vod_id = ids[0]
            url = f"{self.host}/nivod/{vod_id}/"
            
            self.log(f"详情请求: {url}")
            
            rsp = self.fetch(url, headers=self.headers)
            if not rsp or rsp.status_code != 200:
                return {'list': []}
            
            doc = self.html(rsp.text)
            vod_info = self._extract_detail_info(doc, vod_id)
            
            if vod_info:
                self.log(f"详情获取完成 - ID: {vod_id}, 标题: {vod_info.get('vod_name', '')}")
                return {'list': [vod_info]}
            else:
                return {'list': []}
                
        except Exception as e:
            self.log(f"详情获取出错: {str(e)}")
            return {'list': []}
    
    def searchContent(self, key, quick, pg='1'):
        """搜索功能 - 修复版"""
        try:
            if not key or not key.strip():
                return {'list': []}

            key = key.strip()

            # 使用正确的搜索URL格式
            import urllib.parse
            encoded_key = urllib.parse.quote(key)

            # 正确的搜索URL格式：/s/-------------/?wd={keyword}
            if int(pg) == 1:
                url = f"{self.host}/s/-------------/?wd={encoded_key}"
            else:
                # 分页格式：/s/{keyword}----------{page}---/
                url = f"{self.host}/s/{encoded_key}----------{pg}---/"

            self.log(f"搜索请求: {url}")

            rsp = self.fetch(url, headers=self.headers)
            if not rsp or rsp.status_code != 200:
                self.log(f"搜索请求失败: {rsp.status_code if rsp else 'None'}")
                return {'list': []}

            doc = self.html(rsp.text)
            videos = self._extract_videos(doc)  # 使用通用提取方法

            # 智能过滤搜索结果
            filtered_videos = self._filter_search_results(videos, key)

            result = {
                'list': filtered_videos,
                'page': int(pg)
            }

            self.log(f"搜索完成 - 关键词: {key}, 结果数: {len(filtered_videos)}")
            return result

        except Exception as e:
            self.log(f"搜索出错: {str(e)}")
            return {'list': []}
    
    def playerContent(self, flag, id, vipFlags):
        """播放链接 - 支持localProxy代理"""
        try:
            # 解析播放参数: vod_id-source-episode
            parts = id.split('-')
            if len(parts) < 3:
                return {'parse': 1, 'url': ''}

            vod_id, source, episode = parts[0], parts[1], parts[2]
            url = f"{self.host}/niplay/{vod_id}-{source}-{episode}/"

            self.log(f"播放请求: {url}")

            rsp = self.fetch(url, headers=self.headers)
            if not rsp or rsp.status_code != 200:
                return {'parse': 1, 'url': ''}

            # 提取真实播放链接
            play_url = self._extract_play_url(rsp.text, url)

            if play_url:
                # 对于m3u8等流媒体链接，使用localProxy代理
                if '.m3u8' in play_url:
                    proxy_url = self._get_proxy_url(play_url, 'm3u8')
                    result = {
                        'parse': 0,
                        'playUrl': '',  # TVBox兼容性要求
                        'url': proxy_url,
                        'header': {}    # TVBox兼容性要求：使用空header
                    }
                else:
                    result = {
                        'parse': 0,
                        'playUrl': '',  # TVBox兼容性要求
                        'url': play_url,
                        'header': {}    # TVBox兼容性要求：使用空header
                    }
            else:
                result = {
                    'parse': 1,
                    'playUrl': '',
                    'url': url,
                    'header': {}
                }

            self.log(f"播放链接获取完成 - ID: {id}")
            return result

        except Exception as e:
            self.log(f"播放链接获取出错: {str(e)}")
            return {'parse': 1, 'url': ''}

    def localProxy(self, param):
        """本地代理方法 - 处理M3U8等流媒体代理请求（PyramidStore标准格式）"""
        try:
            import base64

            # PyramidStore框架标准参数格式：{'url': base64_encoded_url, 'type': 'media_type'}
            if isinstance(param, dict):
                encoded_url = param.get('url', '')
                media_type = param.get('type', 'm3u8')

                if not encoded_url:
                    self.log(f"localProxy: 缺少url参数")
                    return None

                # Base64解码获取真实URL
                try:
                    url = base64.b64decode(encoded_url).decode('utf-8')
                except Exception as e:
                    self.log(f"localProxy: Base64解码失败: {str(e)}")
                    return None

            else:
                self.log(f"localProxy: 不支持的参数类型: {type(param)}, 期望dict格式")
                return None

            if not url or not url.startswith('http'):
                self.log(f"localProxy: URL参数无效: {url}")
                return None

            self.log(f"localProxy代理请求: {url[:100]}{'...' if len(url) > 100 else ''}")

            # 获取视频数据
            try:
                headers = {
                    'User-Agent': self.headers.get('User-Agent', ''),
                    'Referer': self.host,
                    'Accept': '*/*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                }

                rsp = self.fetch(url, headers=headers)

                if rsp and rsp.status_code == 200:
                    self.log(f"localProxy: 成功获取数据，大小: {len(rsp.content)}字节")

                    # 根据媒体类型设置Content-Type
                    if media_type == 'm3u8':
                        content_type = "application/vnd.apple.mpegurl"
                        # 对于m3u8文件，需要处理其中的TS片段链接
                        content = self._process_m3u8_content(rsp.content.decode('utf-8'), url)
                        return [200, content_type, content.encode('utf-8')]
                    elif media_type == 'ts':
                        content_type = "video/mp2t"
                        return [200, content_type, rsp.content]
                    elif media_type == 'mp4':
                        content_type = "video/mp4"
                        return [200, content_type, rsp.content]
                    else:
                        content_type = "application/octet-stream"
                        return [200, content_type, rsp.content]
                else:
                    self.log(f"localProxy: 请求失败，状态码: {rsp.status_code if rsp else 'None'}")
                    return None

            except Exception as e:
                self.log(f"localProxy: 获取视频数据失败: {str(e)}")
                return None

        except Exception as e:
            self.log(f"localProxy: 代理处理失败: {str(e)}")
            return None

    def _process_m3u8_content(self, content, base_url):
        """处理m3u8文件内容，为TS片段生成代理链接"""
        try:
            import urllib.parse
            import base64

            lines = content.strip().split('\n')
            processed_lines = []

            # 获取基础URL（去掉文件名）
            base_path = base_url[:base_url.rfind('/')]

            for line in lines:
                line = line.strip()

                # 如果是TS片段链接
                if line and not line.startswith('#'):
                    # 构建完整的TS链接
                    if line.startswith('http'):
                        ts_url = line
                    elif line.startswith('/'):
                        # 绝对路径
                        parsed_base = urllib.parse.urlparse(base_url)
                        ts_url = f"{parsed_base.scheme}://{parsed_base.netloc}{line}"
                    else:
                        # 相对路径
                        ts_url = f"{base_path}/{line}"

                    # 为TS片段生成代理链接
                    encoded_ts_url = base64.b64encode(ts_url.encode('utf-8')).decode('utf-8')
                    proxy_ts_url = f"{self.getProxyUrl()}&url={encoded_ts_url}&type=ts"
                    processed_lines.append(proxy_ts_url)
                else:
                    # 保持原始行（注释行等）
                    processed_lines.append(line)

            return '\n'.join(processed_lines)

        except Exception as e:
            self.log(f"处理m3u8内容失败: {str(e)}")
            return content
    # ==================== 私有辅助方法 ====================

    def _get_proxy_url(self, url, media_type='m3u8'):
        """生成代理URL - 使用PyramidStore标准格式"""
        try:
            import base64

            # 使用Base64编码（符合PyramidStore标准）
            encoded_url = base64.b64encode(url.encode('utf-8')).decode('utf-8')

            # 使用标准的getProxyUrl方法
            proxy_base = self.getProxyUrl()

            # 使用PyramidStore标准格式：http://127.0.0.1:9978?do=py&url={base64_encoded}&type={media_type}
            proxy_url = f"{proxy_base}&url={encoded_url}&type={media_type}"
            self.log(f"生成代理URL: {proxy_url}")
            return proxy_url
        except Exception as e:
            self.log(f"生成代理URL失败: {str(e)}")
            return url

    def _proxy_m3u8(self, url):
        """处理m3u8文件代理"""
        try:
            import requests
            from urllib.parse import urlparse, urljoin

            # 获取m3u8文件内容
            response = requests.get(url, headers=self.headers, timeout=10)
            if response.status_code != 200:
                return []

            content = response.text
            lines = content.strip().split('\n')

            # 解析URL基础路径
            parsed_url = urlparse(url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            dir_url = url[:url.rfind('/')]

            # 处理每一行
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('#'):
                    # 处理相对路径
                    if line.startswith('http'):
                        # 绝对路径，需要代理
                        lines[i] = self._get_proxy_url(line, 'ts')
                    elif line.startswith('/'):
                        # 根路径
                        full_url = base_url + line
                        lines[i] = self._get_proxy_url(full_url, 'ts')
                    else:
                        # 相对路径
                        full_url = dir_url + '/' + line
                        lines[i] = self._get_proxy_url(full_url, 'ts')

            # 返回处理后的m3u8内容
            processed_content = '\n'.join(lines)
            return [200, "application/vnd.apple.mpegurl", processed_content.encode('utf-8')]

        except Exception as e:
            self.log(f"m3u8代理处理失败: {str(e)}")
            return []

    def _proxy_media(self, url):
        """处理媒体文件代理"""
        try:
            import requests

            # 流式获取媒体文件
            response = requests.get(url, headers=self.headers, stream=True, timeout=30)
            if response.status_code != 200:
                return []

            content_type = response.headers.get('Content-Type', 'application/octet-stream')
            return [200, content_type, response.content]

        except Exception as e:
            self.log(f"媒体文件代理处理失败: {str(e)}")
            return []

    def _format_url(self, url):
        """格式化URL为完整地址"""
        if not url:
            return ''
        if url.startswith('//'):
            return 'https:' + url
        elif url.startswith('/'):
            return self.host + url
        elif url.startswith('http'):
            return url
        return ''

    def _extract_image(self, element):
        """提取图片URL（优化版，处理懒加载和真实图片）"""
        # 优先查找真实图片URL（包含upload路径的）
        real_pic_selectors = [
            './/img[contains(@src, "upload")]/@src',
            './/img[contains(@data-src, "upload")]/@data-src',
            './/img[contains(@data-original, "upload")]/@data-original'
        ]

        for selector in real_pic_selectors:
            pics = element.xpath(selector)
            if pics:
                return self._format_url(pics[0])

        # 如果没有找到真实图片，查找其他图片但过滤占位符
        fallback_selectors = [
            './/img/@data-original',
            './/img/@data-src',
            './/img/@src'
        ]

        for selector in fallback_selectors:
            pics = element.xpath(selector)
            for p in pics:
                if (p and not any(placeholder in p for placeholder in [
                    'loading.png', 'placeholder', 'default.jpg', 'noimage'
                ]) and not p.startswith('data:image/') and 'base64' not in p):
                    return self._format_url(p)
        return ''

    def _extract_title(self, element):
        """提取标题"""
        title_selectors = [
            './/a/@title',
            './/h3/text()',
            './/span[@class="title"]/text()',
            './/a/text()'
        ]

        for selector in title_selectors:
            titles = element.xpath(selector)
            for t in titles:
                if t and t.strip() and len(t.strip()) > 1:
                    return t.strip()
        return ''

    def _extract_remarks(self, element):
        """提取备注信息"""
        remark_selectors = [
            './/span[@class="remarks"]/text()',
            './/div[@class="remarks"]/text()',
            './/span[contains(@class, "episode")]/text()'
        ]

        for selector in remark_selectors:
            remarks = element.xpath(selector)
            for r in remarks:
                if r and r.strip():
                    return r.strip()
        return ''

    def _extract_home_videos(self, doc):
        """提取首页推荐视频"""
        videos = []
        try:
            # 基于实际网站结构的选择器 - 查找包含视频信息的链接
            video_elements = doc.xpath('//a[contains(@href, "/nivod/") and @title]')

            seen_ids = set()
            for element in video_elements:
                try:
                    href = element.get('href', '')
                    vod_id = self.regStr(r'/nivod/(\d+)/', href)
                    if not vod_id or vod_id in seen_ids:
                        continue
                    seen_ids.add(vod_id)

                    # 提取标题
                    title = element.get('title', '').strip()
                    if not title:
                        continue

                    # 提取图片 - 优化版，过滤占位符
                    pic = self._extract_image(element)

                    # 提取备注信息（从前面的文本节点查找）
                    remarks = ''
                    # 查找前面的兄弟节点中的文本
                    prev_elements = element.xpath('./preceding-sibling::*//text() | ./text()')
                    for text in prev_elements:
                        text = text.strip()
                        if text and ('集' in text or '完结' in text or '正片' in text or '第' in text):
                            remarks = text
                            break

                    if title and vod_id:
                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks
                        })

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"首页视频提取出错: {str(e)}")

        return videos

    def _extract_videos(self, doc):
        """通用视频信息提取 - 修复版，支持搜索结果"""
        videos = []
        seen_ids = set()

        try:
            # 优先查找带title的链接（首页、分类页）
            video_elements = doc.xpath('//a[contains(@href, "/nivod/") and @title]')

            # 如果没有找到带title的链接，查找所有nivod链接（搜索结果页）
            if not video_elements:
                video_elements = doc.xpath('//a[contains(@href, "/nivod/")]')

            for element in video_elements:
                try:
                    href = element.get('href', '')
                    vod_id = self.regStr(r'/nivod/(\d+)/', href)
                    if not vod_id or vod_id in seen_ids:
                        continue
                    seen_ids.add(vod_id)

                    # 提取标题 - 支持多种来源
                    title = element.get('title', '').strip()
                    if not title:
                        # 尝试从链接文本或子元素中提取标题
                        title_sources = [
                            element.text,
                            element.xpath('.//text()'),
                            element.xpath('./following-sibling::*//text()'),
                            element.xpath('./preceding-sibling::*//text()')
                        ]

                        for source in title_sources:
                            if isinstance(source, list):
                                for text in source:
                                    text = text.strip() if text else ''
                                    if text and len(text) > 2 and not any(skip in text for skip in ['播放', '详情', '第', '集']):
                                        title = text
                                        break
                            elif source:
                                text = source.strip()
                                if text and len(text) > 2:
                                    title = text
                                    break
                            if title:
                                break

                    if not title:
                        continue

                    # 提取图片 - 优化版，过滤占位符
                    img_elements = element.xpath('.//img')
                    pic = ''
                    for img in img_elements:
                        src = img.get('src', '') or img.get('data-src', '') or img.get('data-original', '')
                        if src and not any(placeholder in src for placeholder in [
                            'loading.png', 'placeholder', 'default.jpg', 'noimage'
                        ]) and not src.startswith('data:image/') and 'base64' not in src:
                            pic = self._format_url(src)
                            break

                    # 如果没有找到真实图片，尝试使用优化的图片提取方法
                    if not pic or 'loading.png' in pic:
                        pic = self._extract_image(element)

                    # 提取备注信息（从前面的文本节点查找）
                    remarks = ''
                    # 查找前面的兄弟节点中的文本
                    prev_elements = element.xpath('./preceding-sibling::*//text() | ./text()')
                    for text in prev_elements:
                        text = text.strip()
                        if text and ('集' in text or '完结' in text or '正片' in text or '第' in text):
                            remarks = text
                            break

                    if title and vod_id:
                        # 提取额外的TVBox兼容字段
                        year, area, type_name = self._extract_search_metadata(element)

                        videos.append({
                            'vod_id': vod_id,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks,
                            'vod_year': year,      # TVBox兼容性要求
                            'vod_area': area,      # TVBox兼容性要求
                            'vod_tag': type_name   # TVBox兼容性要求
                        })

                except Exception as e:
                    continue

        except Exception as e:
            self.log(f"视频信息提取出错: {str(e)}")

        return videos

    def _extract_search_videos(self, doc):
        """提取搜索结果视频"""
        # 搜索结果使用与通用方法相同的提取逻辑
        return self._extract_videos(doc)

    def _filter_search_results(self, videos, search_key):
        """智能过滤搜索结果"""
        if not search_key:
            return videos

        filtered = []
        search_key_lower = search_key.lower()

        for video in videos:
            title_lower = video['vod_name'].lower()
            if search_key_lower in title_lower:
                filtered.append(video)

        return filtered

    def _extract_search_metadata(self, element):
        """提取搜索结果的元数据信息（年份、地区、类型）- TVBox兼容性"""
        year = ''
        area = ''
        type_name = ''

        try:
            # 获取元素及其周围的所有文本
            all_texts = []

            # 从当前元素获取文本
            current_texts = element.xpath('.//text()')
            all_texts.extend([text.strip() for text in current_texts if text.strip()])

            # 从父级和兄弟元素获取文本
            parent = element.getparent()
            if parent is not None:
                parent_texts = parent.xpath('.//text()')
                all_texts.extend([text.strip() for text in parent_texts if text.strip()])

            # 合并所有文本用于分析
            all_info_text = ' '.join(all_texts)

            # 提取年份 (2000-2030)
            import re
            year_match = re.search(r'(20[0-3]\d)', all_info_text)
            if year_match:
                year = year_match.group(1)

            # 提取地区
            area_patterns = [
                r'(中国大陆|大陆|内地)',
                r'(香港|港)',
                r'(台湾|台)',
                r'(美国|美)',
                r'(日本|日)',
                r'(韩国|韩)',
                r'(英国|英)',
                r'(法国|法)',
                r'(德国|德)',
                r'(意大利)',
                r'(加拿大)',
                r'(澳大利亚)',
                r'(泰国|泰)',
                r'(印度)'
            ]

            for pattern in area_patterns:
                area_match = re.search(pattern, all_info_text)
                if area_match:
                    area_text = area_match.group(1)
                    # 标准化地区名称
                    if area_text in ['中国大陆', '大陆', '内地']:
                        area = '大陆'
                    elif area_text in ['香港', '港']:
                        area = '香港'
                    elif area_text in ['台湾', '台']:
                        area = '台湾'
                    elif area_text in ['美国', '美']:
                        area = '美国'
                    elif area_text in ['日本', '日']:
                        area = '日本'
                    elif area_text in ['韩国', '韩']:
                        area = '韩国'
                    else:
                        area = area_text
                    break

            # 提取类型/分类
            type_patterns = [
                r'(国产动漫|国漫)',
                r'(日韩动漫|日漫|韩漫)',
                r'(欧美动漫|美漫)',
                r'(动漫电影)',
                r'(动漫|动画)',
                r'(剧情片|剧情)',
                r'(喜剧片|喜剧)',
                r'(动作片|动作)',
                r'(爱情片|爱情)',
                r'(科幻片|科幻)',
                r'(悬疑片|悬疑)',
                r'(恐怖片|恐怖)',
                r'(战争片|战争)',
                r'(历史片|历史)',
                r'(犯罪片|犯罪)',
                r'(奇幻片|奇幻)',
                r'(青春片|青春)',
                r'(古装片|古装)',
                r'(冒险片|冒险)',
                r'(武侠片|武侠)',
                r'(偶像剧|偶像)'
            ]

            for pattern in type_patterns:
                type_match = re.search(pattern, all_info_text)
                if type_match:
                    type_text = type_match.group(1)
                    # 标准化类型名称
                    if type_text in ['国产动漫', '国漫']:
                        type_name = '国产动漫'
                    elif type_text in ['日韩动漫', '日漫', '韩漫']:
                        type_name = '日韩动漫'
                    elif type_text in ['欧美动漫', '美漫']:
                        type_name = '欧美动漫'
                    elif type_text in ['动漫', '动画']:
                        type_name = '动漫'
                    else:
                        type_name = type_text.replace('片', '').replace('剧', '')
                    break

            # 如果没有找到具体类型，根据标题推断
            if not type_name:
                title_lower = all_info_text.lower()
                if any(keyword in title_lower for keyword in ['动漫', '动画', '番', '第一季', '第二季', '第三季']):
                    type_name = '动漫'
                elif any(keyword in title_lower for keyword in ['电影', '影片']):
                    type_name = '电影'
                elif any(keyword in title_lower for keyword in ['电视剧', '连续剧']):
                    type_name = '电视剧'

            # 默认值处理
            if not year:
                year = '2024'  # 默认年份
            if not area:
                area = '大陆'  # 默认地区
            if not type_name:
                type_name = '动漫'  # 默认类型

        except Exception as e:
            # 出错时使用默认值
            year = '2024'
            area = '大陆'
            type_name = '动漫'

        return year, area, type_name

    def _get_page_count(self, doc):
        """获取总页数"""
        try:
            # 查找分页信息
            page_elements = doc.xpath('//a[contains(@href, "---")]/@href')
            max_page = 1

            for href in page_elements:
                page_match = self.regStr(r'---(\d+)---', href)
                if page_match:
                    page_num = int(page_match)
                    if page_num > max_page:
                        max_page = page_num

            return max_page if max_page > 1 else 999

        except Exception as e:
            return 999

    def _extract_detail_info(self, doc, vod_id):
        """提取详情页信息 - 修复版"""
        try:
            # 构建完整的vod信息字典，确保包含所有必需字段
            vod_info = {
                'vod_id': vod_id,
                'vod_name': '',
                'vod_pic': '',
                'vod_remarks': '',
                'vod_year': '',
                'vod_area': '',
                'vod_director': '',
                'vod_actor': '',
                'vod_content': '',
                'vod_play_from': '',
                'vod_play_url': ''
            }

            # 提取标题
            title_elements = doc.xpath('//h1/text() | //title/text() | //h2/text()')
            if title_elements:
                title = title_elements[0].strip()
                # 清理标题，移除多余信息
                title = title.replace('在线观看', '').replace('免费观看', '').replace('高清', '').strip()
                vod_info['vod_name'] = title
            else:
                vod_info['vod_name'] = f"视频{vod_id}"

            # 提取图片
            pic_elements = doc.xpath('//img[contains(@src, "upload") or contains(@src, "thumb")]/@src | //img[@class="lazy"]/@data-src')
            if pic_elements:
                vod_info['vod_pic'] = self._format_url(pic_elements[0])

            # 提取简介
            content_elements = doc.xpath('//div[contains(@class, "content") or contains(@class, "desc") or contains(@class, "intro")]//text()')
            if content_elements:
                content = ''.join(content_elements).strip()
                vod_info['vod_content'] = content[:500]  # 限制长度

            # 提取年份、地区、导演、演员等信息
            info_text = ' '.join(doc.xpath('//text()'))

            # 年份
            year_match = self.regStr(r'(\d{4})', info_text)
            if year_match:
                vod_info['vod_year'] = year_match

            # 地区
            areas = ['大陆', '香港', '台湾', '美国', '韩国', '日本', '泰国', '英国', '法国']
            for area in areas:
                if area in info_text:
                    vod_info['vod_area'] = area
                    break

            # 提取导演和演员信息
            if '导演' in info_text:
                director_match = self.regStr(r'导演[：:]\s*([^，,\n]+)', info_text)
                if director_match:
                    vod_info['vod_director'] = director_match.strip()

            if '主演' in info_text or '演员' in info_text:
                actor_match = self.regStr(r'(?:主演|演员)[：:]\s*([^，,\n]+)', info_text)
                if actor_match:
                    vod_info['vod_actor'] = actor_match.strip()

            # 提取播放源 - 这是关键部分
            play_from, play_url = self._extract_play_sources(doc, vod_id)

            if play_from and play_url:
                vod_info['vod_play_from'] = '$$$'.join(play_from)
                vod_info['vod_play_url'] = '$$$'.join(play_url)
                self.log(f"播放源提取成功: {len(play_from)}个播放源")
            else:
                # 如果没有提取到播放源，创建默认播放源
                self.log("未提取到播放源，创建默认播放源")
                vod_info['vod_play_from'] = '泥视频'
                default_play_url = f"/niplay/{vod_id}-1-1/"
                vod_info['vod_play_url'] = f"正片${default_play_url}"

            return vod_info

        except Exception as e:
            self.log(f"详情信息提取出错: {str(e)}")
            return None

    def _extract_play_sources(self, doc, vod_id):
        """提取播放源信息 - 修复版"""
        play_from = []
        play_url = []

        try:
            # 基于实际HTML结构提取播放源
            # 查找所有播放链接
            play_links = doc.xpath('//a[contains(@href, "/niplay/")]')

            self.log(f"找到播放链接数量: {len(play_links)}")

            if play_links:
                # 按播放源分组
                sources = {}

                for link in play_links:
                    try:
                        href = link.get('href', '')
                        title = link.text or link.get('title', '') or ''
                        title = title.strip()

                        # 提取播放ID: vod_id-source-episode
                        play_match = self.regStr(r'/niplay/(\d+-\d+-\d+)/', href)
                        if not play_match:
                            continue

                        # 解析播放源编号和剧集编号
                        parts = play_match.split('-')
                        if len(parts) >= 3:
                            _, source_id, episode_id = parts[0], parts[1], parts[2]

                            if source_id not in sources:
                                sources[source_id] = []

                            # 优化标题处理：优先使用播放ID中的剧集编号
                            # 这样可以确保每个剧集都有唯一的标识
                            if title:
                                # 移除冗余的视频名称，只保留集数
                                title = title.replace('播放', '').replace('立刻播放', '').strip()

                                # 提取标题中的集数
                                title_episode_match = self.regStr(r'(\d+)', title)

                                # 优先使用播放ID中的剧集编号，确保唯一性
                                if episode_id and episode_id.isdigit():
                                    episode_num = int(episode_id)
                                    title = f"第{episode_num}集"
                                elif title_episode_match:
                                    title = f"第{title_episode_match}集"
                                else:
                                    # 如果都没有集数信息，使用播放ID中的剧集编号
                                    title = f"第{episode_id}集" if episode_id else '正片'
                            else:
                                # 没有标题时，使用播放ID中的剧集编号
                                title = f"第{episode_id}集" if episode_id and episode_id.isdigit() else '正片'

                            # 构建播放URL格式：标题$可执行播放页面URL
                            # 将播放ID转换为可访问的播放页面链接
                            episode_play_url = f"/niplay/{play_match}/"

                            # 检查是否已存在相同的播放ID，避免重复
                            episode_entry = f"{title}${episode_play_url}"
                            if episode_entry not in sources[source_id]:
                                sources[source_id].append(episode_entry)

                    except Exception as e:
                        self.log(f"播放链接处理出错: {str(e)}")
                        continue

                # 如果没有找到播放链接，尝试从页面文本中提取播放源信息
                if not sources:
                    self.log("未找到niplay链接，尝试创建默认播放源")
                    # 创建默认播放源，使用可执行的播放页面URL格式
                    default_play_url = f"/niplay/{vod_id}-1-1/"
                    sources['1'] = [f"正片${default_play_url}"]

                # 构建播放源列表 - 添加SID标识以提高TVBox兼容性
                source_names = {
                    '1': '自营1线',
                    '2': '自营2线',
                    '3': '自营4K',
                    '4': '泥视频',
                    '5': '大陆0线',
                    '6': '大陆5线',
                    '7': '大陆6线',
                    '8': '大陆3线',
                    '9': '全球3线'
                }

                # 按播放源ID排序，确保播放源顺序一致
                for source_id in sorted(sources.keys(), key=lambda x: int(x) if x.isdigit() else 999):
                    if sources[source_id]:
                        source_name = source_names.get(source_id, f"播放源{source_id}")
                        # 添加SID标识，提高TVBox兼容性（参考统一影视格式）
                        source_name_with_sid = f"{source_name}[SID:{source_id}]"
                        play_from.append(source_name_with_sid)

                        # 对剧集进行排序
                        episodes = sources[source_id]
                        # 尝试按集数排序
                        try:
                            episodes.sort(key=lambda x: int(self.regStr(r'(\d+)', x.split('$')[0]) or '0'))
                        except:
                            pass  # 如果排序失败，保持原顺序

                        play_url.append('#'.join(episodes))

            # 如果没有找到播放源，创建默认播放源
            if not play_from:
                self.log("创建默认播放源")
                # 确保变量是列表类型
                if not isinstance(play_from, list):
                    play_from = []
                if not isinstance(play_url, list):
                    play_url = []
                play_from.append("泥视频")
                default_play_url = f"/niplay/{vod_id}-1-1/"
                play_url.append(f"正片${default_play_url}")

        except Exception as e:
            self.log(f"播放源提取出错: {str(e)}")
            # 重新初始化变量，确保类型正确
            play_from = []
            play_url = []
            # 创建默认播放源
            play_from.append("泥视频")
            default_play_url = f"/niplay/{vod_id}-1-1/"
            play_url.append(f"正片${default_play_url}")

        self.log(f"最终播放源: {play_from}")
        self.log(f"播放URL数量: {len(play_url)}")

        return play_from, play_url

    def _extract_play_url(self, html_content, page_url):
        """提取真实播放链接 - 修复版"""
        try:
            # 尝试从页面中提取视频链接
            video_patterns = [
                r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
                r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
                r'video\s*:\s*"([^"]+)"',
                r'source\s*:\s*"([^"]+)"',
                r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
            ]

            for pattern in video_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if match and ('http' in match or match.startswith('//')):
                        # 处理JSON转义字符
                        clean_url = match.replace('\\/', '/')
                        # 进一步清理可能的转义字符
                        clean_url = clean_url.replace('\\"', '"').replace('\\\\', '\\')
                        return self._format_url(clean_url)

            # 如果没有找到直接链接，返回页面URL让播放器解析
            return page_url

        except Exception as e:
            self.log(f"播放链接提取出错: {str(e)}")
            return page_url
    

    
    def _extract_title(self, element):
        """提取标题"""
        title_selectors = [
            './/a/@title',
            './/h3/text()',
            './/span[@class="title"]/text()',
            './/a/text()'
        ]
        
        for selector in title_selectors:
            titles = element.xpath(selector)
            for t in titles:
                if t and t.strip() and len(t.strip()) > 1:
                    return t.strip()
        return ''
    
    def _extract_remarks(self, element):
        """提取备注信息"""
        remark_selectors = [
            './/span[@class="remarks"]/text()',
            './/div[@class="remarks"]/text()',
            './/span[contains(@class, "episode")]/text()'
        ]
        
        for selector in remark_selectors:
            remarks = element.xpath(selector)
            for r in remarks:
                if r and r.strip():
                    return r.strip()
        return ''
