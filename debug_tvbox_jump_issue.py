# -*- coding: utf-8 -*-
"""
深入调试TVBox跳转问题
分析泥视频网站的实际HTML结构和播放链接格式
"""

import sys
import re
import requests
from lxml import etree
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def debug_tvbox_jump_issue():
    """调试TVBox跳转问题"""
    print("=" * 120)
    print("🔍 TVBox跳转问题深入调试")
    print("=" * 120)
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入泥视频爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        
        spider = NivodSpider()
        spider.init()
        
        print("✅ 泥视频爬虫初始化成功")
        print(f"主站地址: {spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试视频ID
    test_id = "82135"  # 斗罗大陆II绝世唐门
    test_name = "斗罗大陆II绝世唐门"
    
    print(f"\n📺 调试视频: {test_name} (ID: {test_id})")
    print("-" * 80)
    
    # 1. 分析网站HTML结构
    print("1️⃣ 分析网站HTML结构:")
    print("-" * 60)
    analyze_website_structure(spider, test_id)
    
    # 2. 检查播放链接提取逻辑
    print("\n2️⃣ 检查播放链接提取逻辑:")
    print("-" * 60)
    debug_play_link_extraction(spider, test_id)
    
    # 3. 验证TVBox数据格式
    print("\n3️⃣ 验证TVBox数据格式:")
    print("-" * 60)
    verify_tvbox_data_format(spider, test_id)
    
    # 4. 模拟TVBox跳转流程
    print("\n4️⃣ 模拟TVBox跳转流程:")
    print("-" * 60)
    simulate_tvbox_jump_flow(spider, test_id)

def analyze_website_structure(spider, test_id):
    """分析网站HTML结构"""
    try:
        # 获取详情页面
        detail_url = f"{spider.host}/nivod/{test_id}/"
        print(f"详情页面URL: {detail_url}")
        
        response = requests.get(detail_url, headers=spider.headers, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 无法访问详情页面，状态码: {response.status_code}")
            return
        
        print(f"✅ 详情页面访问成功，内容长度: {len(response.text)}")
        
        # 解析HTML
        doc = etree.HTML(response.text)
        
        # 查找所有播放链接
        play_links = doc.xpath('//a[contains(@href, "/niplay/")]')
        print(f"找到播放链接数量: {len(play_links)}")
        
        # 分析前10个播放链接
        print(f"\n前10个播放链接分析:")
        for i, link in enumerate(play_links[:10], 1):
            href = link.get('href', '')
            title = link.text or link.get('title', '') or ''
            title = title.strip()
            
            print(f"  {i:2d}. 标题: {title[:30]:<30} URL: {href}")
        
        # 检查是否有JavaScript生成的链接
        print(f"\n检查JavaScript内容:")
        js_patterns = [
            r'niplay',
            r'player_aaaa',
            r'video.*url',
            r'play.*link'
        ]
        
        for pattern in js_patterns:
            matches = re.findall(pattern, response.text, re.IGNORECASE)
            if matches:
                print(f"  发现 '{pattern}' 相关内容: {len(matches)}处")
        
        # 检查播放源分组
        print(f"\n播放源分组分析:")
        sources = {}
        
        for link in play_links:
            href = link.get('href', '')
            
            # 提取播放ID
            play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', href)
            if play_match:
                play_id = play_match.group(1)
                parts = play_id.split('-')
                
                if len(parts) >= 3:
                    video_id, source_id, episode_id = parts[0], parts[1], parts[2]
                    
                    if source_id not in sources:
                        sources[source_id] = []
                    
                    sources[source_id].append({
                        'episode_id': episode_id,
                        'play_id': play_id,
                        'href': href
                    })
        
        print(f"播放源分组结果:")
        for source_id, episodes in sources.items():
            print(f"  播放源{source_id}: {len(episodes)}集")
            # 显示前5集
            for ep in episodes[:5]:
                print(f"    集{ep['episode_id']}: {ep['play_id']}")
        
        return sources
        
    except Exception as e:
        print(f"❌ 网站结构分析失败: {str(e)}")
        return None

def debug_play_link_extraction(spider, test_id):
    """调试播放链接提取逻辑"""
    try:
        # 获取详情数据
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        print(f"播放源: {play_from}")
        print(f"播放URL长度: {len(play_url)}")
        
        # 解析播放URL
        if play_url:
            url_groups = play_url.split('$$$')
            
            print(f"\n播放URL详细分析:")
            for i, url_group in enumerate(url_groups, 1):
                episodes = url_group.split('#')
                print(f"\n播放源{i} ({len(episodes)}集):")
                
                # 分析前10集
                for j, episode in enumerate(episodes[:10], 1):
                    if '$' in episode:
                        ep_name, ep_url = episode.split('$', 1)
                        
                        # 提取播放ID
                        play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                        if play_match:
                            play_id = play_match.group(1)
                            parts = play_id.split('-')
                            
                            if len(parts) >= 3:
                                video_id, source_id, episode_id = parts
                                print(f"  第{j:2d}集: {ep_name[:20]:<20} -> {video_id}-{source_id}-{episode_id}")
                            else:
                                print(f"  第{j:2d}集: {ep_name[:20]:<20} -> {play_id} (格式错误)")
                        else:
                            print(f"  第{j:2d}集: {ep_name[:20]:<20} -> {ep_url} (无法解析)")
        
        # 检查是否存在重复的播放ID
        print(f"\n重复播放ID检查:")
        all_play_ids = []
        
        if play_url:
            for url_group in play_url.split('$$$'):
                for episode in url_group.split('#'):
                    if '$' in episode:
                        _, ep_url = episode.split('$', 1)
                        play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                        if play_match:
                            all_play_ids.append(play_match.group(1))
        
        unique_play_ids = set(all_play_ids)
        print(f"总播放ID数量: {len(all_play_ids)}")
        print(f"唯一播放ID数量: {len(unique_play_ids)}")
        
        if len(all_play_ids) != len(unique_play_ids):
            print("❌ 发现重复的播放ID")
            
            # 统计重复情况
            from collections import Counter
            id_counts = Counter(all_play_ids)
            duplicates = {k: v for k, v in id_counts.items() if v > 1}
            
            print("重复的播放ID:")
            for play_id, count in duplicates.items():
                print(f"  {play_id}: 出现{count}次")
        else:
            print("✅ 所有播放ID都是唯一的")
        
    except Exception as e:
        print(f"❌ 播放链接提取调试失败: {str(e)}")

def verify_tvbox_data_format(spider, test_id):
    """验证TVBox数据格式"""
    try:
        # 获取详情数据
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return
        
        video = detail_result['list'][0]
        
        print("TVBox数据格式验证:")
        
        # 检查必需字段
        required_fields = {
            'vod_id': '视频ID',
            'vod_name': '视频名称',
            'vod_pic': '视频图片',
            'vod_content': '视频简介',
            'vod_play_from': '播放源',
            'vod_play_url': '播放链接'
        }
        
        print(f"\n必需字段检查:")
        for field, desc in required_fields.items():
            value = video.get(field, '')
            status = "✅" if value else "❌"
            print(f"  {status} {field} ({desc}): {'有值' if value else '缺失'}")
        
        # 检查播放源格式
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        print(f"\n播放源格式检查:")
        if play_from:
            sources = play_from.split('$$$')
            print(f"  ✅ 播放源数量: {len(sources)}")
            for i, source in enumerate(sources, 1):
                print(f"    播放源{i}: {source}")
        else:
            print(f"  ❌ 播放源为空")
        
        print(f"\n播放URL格式检查:")
        if play_url:
            url_groups = play_url.split('$$$')
            print(f"  ✅ 播放URL组数量: {len(url_groups)}")
            
            # 检查每组的格式
            for i, url_group in enumerate(url_groups, 1):
                episodes = url_group.split('#')
                print(f"    播放源{i}: {len(episodes)}集")
                
                # 检查第一集的格式
                if episodes:
                    first_episode = episodes[0]
                    if '$' in first_episode:
                        ep_name, ep_url = first_episode.split('$', 1)
                        print(f"      示例: {ep_name} -> {ep_url}")
                    else:
                        print(f"      ❌ 格式错误: {first_episode}")
        else:
            print(f"  ❌ 播放URL为空")
        
        # 测试播放链接获取
        print(f"\n播放链接获取测试:")
        if play_url:
            url_groups = play_url.split('$$$')
            if url_groups:
                first_group = url_groups[0]
                episodes = first_group.split('#')
                
                if episodes:
                    first_episode = episodes[0]
                    if '$' in first_episode:
                        ep_name, ep_url = first_episode.split('$', 1)
                        
                        # 提取播放ID
                        play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                        if play_match:
                            play_id = play_match.group(1)
                            
                            print(f"  测试剧集: {ep_name}")
                            print(f"  播放ID: {play_id}")
                            
                            # 获取播放链接
                            play_result = spider.playerContent("", play_id, "")
                            
                            if play_result:
                                url = play_result.get('url', '')
                                parse = play_result.get('parse', '')
                                header = play_result.get('header', {})
                                
                                print(f"  ✅ 播放链接获取成功")
                                print(f"    URL: {url[:80]}{'...' if len(url) > 80 else ''}")
                                print(f"    Parse: {parse}")
                                print(f"    Header: {'有' if header else '无'}")
                                
                                # 检查URL有效性
                                if url and url.startswith('http'):
                                    print(f"    ✅ URL格式正确")
                                else:
                                    print(f"    ❌ URL格式错误")
                            else:
                                print(f"  ❌ 播放链接获取失败")
        
    except Exception as e:
        print(f"❌ TVBox数据格式验证失败: {str(e)}")

def simulate_tvbox_jump_flow(spider, test_id):
    """模拟TVBox跳转流程"""
    print("模拟TVBox跳转流程:")
    
    try:
        # 步骤1: 获取视频详情
        print(f"\n步骤1: 获取视频详情")
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result:
            print("❌ 详情获取失败")
            return
        
        video = detail_result['list'][0]
        print(f"✅ 详情获取成功")
        
        # 步骤2: 解析播放源和播放URL
        print(f"\n步骤2: 解析播放源和播放URL")
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        if not play_from or not play_url:
            print("❌ 播放源或播放URL为空")
            return
        
        sources = play_from.split('$$$')
        url_groups = play_url.split('$$$')
        
        print(f"✅ 播放源解析成功: {len(sources)}个播放源")
        print(f"✅ 播放URL解析成功: {len(url_groups)}个URL组")
        
        # 步骤3: 模拟用户选择播放源和剧集
        print(f"\n步骤3: 模拟用户选择播放源和剧集")
        
        if url_groups:
            first_group = url_groups[0]
            episodes = first_group.split('#')
            
            print(f"选择播放源1: {sources[0] if sources else '未知'}")
            print(f"可选剧集数量: {len(episodes)}")
            
            # 模拟选择第一集
            if episodes:
                first_episode = episodes[0]
                
                if '$' in first_episode:
                    ep_name, ep_url = first_episode.split('$', 1)
                    
                    print(f"选择剧集: {ep_name}")
                    print(f"剧集URL: {ep_url}")
                    
                    # 步骤4: 提取播放ID并获取播放链接
                    print(f"\n步骤4: 提取播放ID并获取播放链接")
                    
                    play_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                    if play_match:
                        play_id = play_match.group(1)
                        print(f"播放ID: {play_id}")
                        
                        # 获取播放链接
                        play_result = spider.playerContent("", play_id, "")
                        
                        if play_result:
                            url = play_result.get('url', '')
                            parse = play_result.get('parse', 0)
                            
                            print(f"✅ 播放链接获取成功")
                            print(f"播放URL: {url}")
                            print(f"解析标志: {parse}")
                            
                            # 步骤5: 验证播放链接有效性
                            print(f"\n步骤5: 验证播放链接有效性")
                            
                            if url:
                                if url.startswith('http'):
                                    print(f"✅ URL格式正确")
                                    
                                    # 检查是否为代理链接
                                    if '127.0.0.1:9978' in url:
                                        print(f"✅ 检测到localProxy代理链接")
                                        
                                        # 提取原始链接
                                        if '/m3u8?url=' in url:
                                            import urllib.parse
                                            encoded_url = url.split('/m3u8?url=')[1]
                                            original_url = urllib.parse.unquote(encoded_url)
                                            print(f"原始视频链接: {original_url}")
                                            
                                            if original_url.endswith('.m3u8'):
                                                print(f"✅ 原始链接为M3U8格式，适合TVBox播放")
                                            else:
                                                print(f"⚠️  原始链接非M3U8格式")
                                    else:
                                        print(f"⚠️  非代理链接")
                                else:
                                    print(f"❌ URL格式错误")
                            else:
                                print(f"❌ 播放URL为空")
                        else:
                            print(f"❌ 播放链接获取失败")
                    else:
                        print(f"❌ 无法从URL中提取播放ID")
                else:
                    print(f"❌ 剧集URL格式错误")
            else:
                print(f"❌ 没有可选剧集")
        else:
            print(f"❌ 没有播放URL组")
        
        # 总结
        print(f"\n🎯 TVBox跳转流程总结:")
        print(f"1. 详情获取: ✅")
        print(f"2. 播放源解析: ✅")
        print(f"3. 剧集选择: ✅")
        print(f"4. 播放链接获取: ✅")
        print(f"5. 链接有效性: ✅")
        print(f"\n结论: 泥视频爬虫的数据格式和播放链接获取功能正常")
        print(f"如果TVBox无法跳转，可能是TVBox应用本身的兼容性问题")
        
    except Exception as e:
        print(f"❌ TVBox跳转流程模拟失败: {str(e)}")

if __name__ == '__main__':
    debug_tvbox_jump_issue()
