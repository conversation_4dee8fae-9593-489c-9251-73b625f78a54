# -*- coding: utf-8 -*-
"""
测试统一影视爬虫的vod_play_from格式
详细分析播放源名称的结构和格式
"""

import sys

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_tongyiys_playfrom_format():
    """测试统一影视播放源格式"""
    print("=" * 80)
    print("🔍 测试统一影视爬虫vod_play_from格式")
    print("=" * 80)
    
    # 导入统一影视爬虫
    try:
        from 统一影视 import Spider
        spider = Spider()
        spider.init()
        print("✅ 统一影视爬虫初始化成功")
        print(f"网站地址: {spider.host}")
    except Exception as e:
        print(f"❌ 统一影视爬虫初始化失败: {str(e)}")
        return
    
    # 测试多个视频ID
    test_cases = [
        {"search": "斗罗大陆", "name": "斗罗大陆系列"},
        {"search": "凡人修仙传", "name": "凡人修仙传"},
    ]
    
    for i, test_case in enumerate(test_cases):
        search_key = test_case["search"]
        test_name = test_case["name"]
        
        print(f"\n{i+1}. 搜索测试: {test_name}")
        print("-" * 60)
        
        try:
            # 先搜索获取视频ID
            search_result = spider.searchContent(search_key, False, "1")
            if search_result and 'list' in search_result and search_result['list']:
                # 取前3个视频进行测试
                test_videos = search_result['list'][:3]
                
                for j, video in enumerate(test_videos):
                    test_id = video['vod_id']
                    video_name = video['vod_name']
                    
                    print(f"\n  视频 {j+1}: {video_name} (ID: {test_id})")
                    print("  " + "-" * 50)
                    
                    # 测试detailContent方法
                    detail_result = spider.detailContent([test_id])
                    
                    if detail_result and 'list' in detail_result and detail_result['list']:
                        video_detail = detail_result['list'][0]
                        
                        # 重点分析播放源格式
                        play_from = video_detail.get('vod_play_from', '')
                        play_url = video_detail.get('vod_play_url', '')
                        
                        print(f"  📺 播放源格式分析:")
                        print(f"  vod_play_from: {play_from}")
                        print(f"  vod_play_from长度: {len(play_from)}")
                        
                        if play_from:
                            # 分析播放源结构
                            sources = play_from.split('$$$')
                            print(f"  播放源数量: {len(sources)}")
                            
                            # 详细分析每个播放源
                            for k, source in enumerate(sources):
                                print(f"    播放源 {k+1}: {source}")
                                
                                # 分析播放源名称格式
                                if '[SID:' in source and ']' in source:
                                    # 提取播放源名称和SID
                                    import re
                                    match = re.match(r'(.+)\[SID:(\d+)\]', source)
                                    if match:
                                        source_name = match.group(1)
                                        sid = match.group(2)
                                        print(f"      播放源名称: {source_name}")
                                        print(f"      播放源ID(SID): {sid}")
                                    else:
                                        print(f"      格式异常: 无法解析SID")
                                else:
                                    print(f"      格式: 简单名称（无SID标识）")
                            
                            # 分析播放源命名规律
                            print(f"\n  🔍 播放源命名规律分析:")
                            source_names = [s.split('[SID:')[0] if '[SID:' in s else s for s in sources]
                            
                            # 统计命名类型
                            naming_types = {
                                '数字+清晰度': [],
                                '清晰度': [],
                                '播放源+数字': [],
                                '备用+数字': [],
                                '其他': []
                            }
                            
                            for name in source_names:
                                if re.match(r'\d+.*[清晰度]', name):
                                    naming_types['数字+清晰度'].append(name)
                                elif any(keyword in name for keyword in ['超清', '蓝光', '高清', '标清']):
                                    naming_types['清晰度'].append(name)
                                elif '播放源' in name:
                                    naming_types['播放源+数字'].append(name)
                                elif '备用' in name:
                                    naming_types['备用+数字'].append(name)
                                else:
                                    naming_types['其他'].append(name)
                            
                            for naming_type, names in naming_types.items():
                                if names:
                                    print(f"    {naming_type}: {names}")
                            
                            # 检查SID分布
                            sids = []
                            for source in sources:
                                if '[SID:' in source:
                                    sid_match = re.search(r'\[SID:(\d+)\]', source)
                                    if sid_match:
                                        sids.append(int(sid_match.group(1)))
                            
                            if sids:
                                print(f"  SID分布: {sorted(sids)}")
                                print(f"  SID范围: {min(sids)} - {max(sids)}")
                                print(f"  SID是否连续: {'是' if sids == list(range(min(sids), max(sids)+1)) else '否'}")
                            
                        else:
                            print("  ❌ 播放源为空")
                            
                    else:
                        print("  ❌ detailContent返回空结果")
                        
            else:
                print(f"❌ 搜索 '{search_key}' 未找到结果")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # 总结播放源格式特点
    print(f"\n📊 统一影视播放源格式总结")
    print("=" * 60)
    
    print("🔍 vod_play_from格式特点:")
    print("1. 使用$$$分隔多个播放源")
    print("2. 每个播放源格式: 播放源名称[SID:播放源ID]")
    print("3. 播放源名称类型:")
    print("   - 清晰度类型: 1080、超清、蓝光5等")
    print("   - 播放源类型: 播放源5、播放源10等")
    print("   - 备用类型: 备用4、备用5等")
    print("4. SID用于标识不同的播放源，与URL中的sid参数对应")
    
    print("\n🎯 与泥视频对比:")
    print("统一影视: 1080[SID:3]$$$超清[SID:9]$$$蓝光5[SID:6]")
    print("泥视频:   自营1线$$$自营2线$$$自营4K")
    print("差异: 统一影视包含SID标识，泥视频使用简单名称")

if __name__ == '__main__':
    test_tongyiys_playfrom_format()
