# -*- coding: utf-8 -*-
"""
TVBox兼容性详细对比分析
对比统一影视和泥视频爬虫在TVBox中的跳转机制和数据格式
"""

import sys
import json
import re
from datetime import datetime

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def analyze_tvbox_compatibility():
    """分析TVBox兼容性问题"""
    print("=" * 120)
    print("📱 TVBox兼容性详细对比分析")
    print("=" * 120)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 导入两个爬虫
    try:
        from 统一影视 import Spider as UnitySpider
        from 泥视频 import Spider as NivodSpider
        
        unity_spider = UnitySpider()
        unity_spider.init()
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        print("✅ 爬虫初始化成功")
        print(f"统一影视: {unity_spider.host}")
        print(f"泥视频: {nivod_spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 测试用例
    test_cases = [
        {
            "unity_id": "73268",
            "nivod_id": "82135", 
            "name": "动漫测试",
            "description": "多集动漫对比"
        }
    ]
    
    for test_case in test_cases:
        unity_id = test_case["unity_id"]
        nivod_id = test_case["nivod_id"]
        test_name = test_case["name"]
        
        print(f"\n{'='*100}")
        print(f"🎬 {test_name} - 跳转机制对比分析")
        print(f"统一影视ID: {unity_id}, 泥视频ID: {nivod_id}")
        print(f"{'='*100}")
        
        # 1. 跳转机制对比
        print(f"\n1️⃣ 跳转机制对比:")
        print("-" * 80)
        unity_jump_data = analyze_unity_jump_mechanism(unity_spider, unity_id)
        nivod_jump_data = analyze_nivod_jump_mechanism(nivod_spider, nivod_id)
        
        # 2. 数据结构验证
        print(f"\n2️⃣ 数据结构验证:")
        print("-" * 80)
        compare_data_structures(unity_jump_data, nivod_jump_data)
        
        # 3. TVBox兼容性分析
        print(f"\n3️⃣ TVBox兼容性分析:")
        print("-" * 80)
        analyze_tvbox_requirements(unity_jump_data, nivod_jump_data)
        
        # 4. 问题定位和修复建议
        print(f"\n4️⃣ 问题定位和修复建议:")
        print("-" * 80)
        identify_issues_and_solutions(unity_jump_data, nivod_jump_data)

def analyze_unity_jump_mechanism(spider, test_id):
    """分析统一影视跳转机制"""
    print("🔍 统一影视跳转机制分析:")
    
    try:
        # 获取详情
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取统一影视详情失败")
            return None
            
        video = detail_result['list'][0]
        
        # 提取关键数据
        jump_data = {
            'spider_name': '统一影视',
            'video_id': test_id,
            'vod_id': video.get('vod_id', ''),
            'vod_name': video.get('vod_name', ''),
            'vod_play_from': video.get('vod_play_from', ''),
            'vod_play_url': video.get('vod_play_url', ''),
            'detail_structure': video,
            'play_sources': [],
            'play_urls': [],
            'sample_play_results': []
        }
        
        print(f"视频ID: {jump_data['vod_id']}")
        print(f"视频名称: {jump_data['vod_name']}")
        print(f"播放源: {jump_data['vod_play_from']}")
        print(f"播放URL长度: {len(jump_data['vod_play_url'])}")
        
        # 解析播放源和URL
        play_from = jump_data['vod_play_from']
        play_url = jump_data['vod_play_url']
        
        if play_from:
            sources = play_from.split('$$$')
            jump_data['play_sources'] = sources
            print(f"播放源数量: {len(sources)}")
            for i, source in enumerate(sources[:3]):  # 只显示前3个
                print(f"  播放源{i+1}: {source}")
        
        if play_url:
            url_groups = play_url.split('$$$')
            jump_data['play_urls'] = url_groups
            print(f"播放URL组数量: {len(url_groups)}")
            
            # 分析第一个播放源的URL格式
            if url_groups:
                first_group = url_groups[0]
                episodes = first_group.split('#')[:3]  # 只分析前3集
                
                print(f"第一个播放源的剧集数量: {len(first_group.split('#'))}")
                print(f"前3集URL格式:")
                
                for i, episode in enumerate(episodes, 1):
                    if '$' in episode:
                        ep_name, ep_url = episode.split('$', 1)
                        print(f"  第{i}集: {ep_name} -> {ep_url}")
                        
                        # 测试播放链接获取
                        try:
                            # 从URL中提取播放参数
                            url_match = re.search(r'/vod/play/id/(\d+)/sid/(\d+)/nid/(\d+)\.html', ep_url)
                            if url_match:
                                vid, sid, nid = url_match.groups()
                                play_id = f"{vid}-{sid}-{nid}"
                                
                                play_result = spider.playerContent("", play_id, "")
                                
                                if play_result:
                                    jump_data['sample_play_results'].append({
                                        'episode': ep_name,
                                        'play_id': play_id,
                                        'result': play_result
                                    })
                                    
                                    print(f"    播放链接: {play_result.get('url', '无')}")
                                    print(f"    解析标志: {play_result.get('parse', '无')}")
                        except Exception as e:
                            print(f"    播放链接获取失败: {str(e)}")
        
        return jump_data
        
    except Exception as e:
        print(f"❌ 统一影视跳转机制分析失败: {str(e)}")
        return None

def analyze_nivod_jump_mechanism(spider, test_id):
    """分析泥视频跳转机制"""
    print("\n🔍 泥视频跳转机制分析:")
    
    try:
        # 获取详情
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取泥视频详情失败")
            return None
            
        video = detail_result['list'][0]
        
        # 提取关键数据
        jump_data = {
            'spider_name': '泥视频',
            'video_id': test_id,
            'vod_id': video.get('vod_id', ''),
            'vod_name': video.get('vod_name', ''),
            'vod_play_from': video.get('vod_play_from', ''),
            'vod_play_url': video.get('vod_play_url', ''),
            'detail_structure': video,
            'play_sources': [],
            'play_urls': [],
            'sample_play_results': []
        }
        
        print(f"视频ID: {jump_data['vod_id']}")
        print(f"视频名称: {jump_data['vod_name']}")
        print(f"播放源: {jump_data['vod_play_from']}")
        print(f"播放URL长度: {len(jump_data['vod_play_url'])}")
        
        # 解析播放源和URL
        play_from = jump_data['vod_play_from']
        play_url = jump_data['vod_play_url']
        
        if play_from:
            sources = play_from.split('$$$')
            jump_data['play_sources'] = sources
            print(f"播放源数量: {len(sources)}")
            for i, source in enumerate(sources[:3]):  # 只显示前3个
                print(f"  播放源{i+1}: {source}")
        
        if play_url:
            url_groups = play_url.split('$$$')
            jump_data['play_urls'] = url_groups
            print(f"播放URL组数量: {len(url_groups)}")
            
            # 分析第一个播放源的URL格式
            if url_groups:
                first_group = url_groups[0]
                episodes = first_group.split('#')[:3]  # 只分析前3集
                
                print(f"第一个播放源的剧集数量: {len(first_group.split('#'))}")
                print(f"前3集URL格式:")
                
                for i, episode in enumerate(episodes, 1):
                    if '$' in episode:
                        ep_name, ep_url = episode.split('$', 1)
                        print(f"  第{i}集: {ep_name} -> {ep_url}")
                        
                        # 测试播放链接获取
                        try:
                            # 从URL中提取播放参数
                            url_match = re.search(r'/niplay/(\d+-\d+-\d+)/', ep_url)
                            if url_match:
                                play_id = url_match.group(1)
                                
                                play_result = spider.playerContent("", play_id, "")
                                
                                if play_result:
                                    jump_data['sample_play_results'].append({
                                        'episode': ep_name,
                                        'play_id': play_id,
                                        'result': play_result
                                    })
                                    
                                    print(f"    播放链接: {play_result.get('url', '无')}")
                                    print(f"    解析标志: {play_result.get('parse', '无')}")
                        except Exception as e:
                            print(f"    播放链接获取失败: {str(e)}")
        
        return jump_data
        
    except Exception as e:
        print(f"❌ 泥视频跳转机制分析失败: {str(e)}")
        return None

def compare_data_structures(unity_data, nivod_data):
    """对比数据结构"""
    print("📊 数据结构对比:")

    if not unity_data or not nivod_data:
        print("❌ 缺少对比数据")
        return

    # 对比关键字段
    key_fields = ['vod_id', 'vod_name', 'vod_play_from', 'vod_play_url']

    print(f"\n关键字段对比:")
    print("-" * 60)

    for field in key_fields:
        unity_value = unity_data.get(field, '')
        nivod_value = nivod_data.get(field, '')

        print(f"{field}:")
        print(f"  统一影视: {unity_value[:100]}{'...' if len(str(unity_value)) > 100 else ''}")
        print(f"  泥视频:   {nivod_value[:100]}{'...' if len(str(nivod_value)) > 100 else ''}")
        print()

    # 对比播放源格式
    print(f"播放源格式对比:")
    print("-" * 60)

    unity_sources = unity_data.get('play_sources', [])
    nivod_sources = nivod_data.get('play_sources', [])

    print(f"统一影视播放源: {unity_sources}")
    print(f"泥视频播放源:   {nivod_sources}")

    # 对比URL格式
    print(f"\nURL格式对比:")
    print("-" * 60)

    unity_urls = unity_data.get('play_urls', [])
    nivod_urls = nivod_data.get('play_urls', [])

    if unity_urls and nivod_urls:
        # 分析第一个播放源的第一集
        unity_first = unity_urls[0].split('#')[0] if unity_urls[0] else ''
        nivod_first = nivod_urls[0].split('#')[0] if nivod_urls[0] else ''

        print(f"统一影视第一集URL: {unity_first}")
        print(f"泥视频第一集URL:   {nivod_first}")

        # 分析URL模式
        unity_pattern = extract_url_pattern(unity_first)
        nivod_pattern = extract_url_pattern(nivod_first)

        print(f"\nURL模式分析:")
        print(f"统一影视模式: {unity_pattern}")
        print(f"泥视频模式:   {nivod_pattern}")

def extract_url_pattern(url_string):
    """提取URL模式"""
    if '$' in url_string:
        _, url = url_string.split('$', 1)

        # 统一影视模式: /index.php/vod/play/id/{id}/sid/{sid}/nid/{nid}.html
        if '/vod/play/' in url:
            return "统一影视标准格式: /index.php/vod/play/id/{id}/sid/{sid}/nid/{nid}.html"

        # 泥视频模式: /niplay/{id}-{sid}-{nid}/
        elif '/niplay/' in url:
            return "泥视频简化格式: /niplay/{id}-{sid}-{nid}/"

        else:
            return f"未知格式: {url}"

    return "无法解析"

def analyze_tvbox_requirements(unity_data, nivod_data):
    """分析TVBox兼容性要求"""
    print("📱 TVBox兼容性要求分析:")

    # TVBox对数据格式的要求
    tvbox_requirements = {
        'vod_id': '必须为字符串，唯一标识',
        'vod_name': '必须为字符串，视频名称',
        'vod_play_from': '播放源列表，用$$$分隔',
        'vod_play_url': '播放链接列表，用$$$分隔播放源，用#分隔剧集',
        'playerContent_parse': '0表示直接播放，1表示需要解析',
        'playerContent_url': '必须为可播放的视频链接',
        'playerContent_header': '可选的请求头信息'
    }

    print(f"\nTVBox数据格式要求:")
    print("-" * 60)
    for field, requirement in tvbox_requirements.items():
        print(f"{field}: {requirement}")

    # 验证统一影视兼容性
    print(f"\n统一影视TVBox兼容性验证:")
    print("-" * 60)
    verify_tvbox_compatibility(unity_data, "统一影视")

    # 验证泥视频兼容性
    print(f"\n泥视频TVBox兼容性验证:")
    print("-" * 60)
    verify_tvbox_compatibility(nivod_data, "泥视频")

def verify_tvbox_compatibility(data, spider_name):
    """验证TVBox兼容性"""
    if not data:
        print(f"❌ {spider_name}: 无数据")
        return

    issues = []

    # 检查基本字段
    required_fields = ['vod_id', 'vod_name', 'vod_play_from', 'vod_play_url']
    for field in required_fields:
        value = data.get(field, '')
        if not value:
            issues.append(f"缺少{field}字段")
        elif not isinstance(value, str):
            issues.append(f"{field}字段类型错误")

    # 检查播放源格式
    play_from = data.get('vod_play_from', '')
    if play_from:
        if '$$$' in play_from:
            sources = play_from.split('$$$')
            print(f"✅ 播放源格式正确: {len(sources)}个播放源")
        else:
            print(f"✅ 单播放源格式: {play_from}")

    # 检查播放URL格式
    play_url = data.get('vod_play_url', '')
    if play_url:
        if '$$$' in play_url:
            url_groups = play_url.split('$$$')
            print(f"✅ 播放URL格式正确: {len(url_groups)}个播放源组")
        else:
            print(f"✅ 单播放源URL: 剧集数量{len(play_url.split('#'))}")

    # 检查播放结果
    sample_results = data.get('sample_play_results', [])
    if sample_results:
        print(f"播放链接测试结果:")
        for result in sample_results[:2]:  # 只显示前2个
            episode = result['episode']
            play_result = result['result']

            url = play_result.get('url', '')
            parse = play_result.get('parse', '')

            print(f"  {episode}:")
            print(f"    URL: {url[:80]}{'...' if len(url) > 80 else ''}")
            print(f"    Parse: {parse}")

            # 检查URL有效性
            if url:
                if url.startswith('http'):
                    print(f"    ✅ URL格式正确")
                else:
                    print(f"    ❌ URL格式错误")
                    issues.append(f"{episode} URL格式错误")
            else:
                print(f"    ❌ URL为空")
                issues.append(f"{episode} URL为空")

    # 总结
    if issues:
        print(f"\n❌ {spider_name}兼容性问题:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print(f"\n✅ {spider_name}基本兼容性良好")

def identify_issues_and_solutions(unity_data, nivod_data):
    """识别问题并提供解决方案"""
    print("🔧 问题识别和解决方案:")

    if not unity_data or not nivod_data:
        print("❌ 缺少对比数据")
        return

    issues = []
    solutions = []

    # 对比播放结果
    unity_results = unity_data.get('sample_play_results', [])
    nivod_results = nivod_data.get('sample_play_results', [])

    print(f"\n播放结果对比:")
    print("-" * 60)

    if unity_results and nivod_results:
        unity_result = unity_results[0]['result']
        nivod_result = nivod_results[0]['result']

        print(f"统一影视播放结果:")
        print(f"  URL: {unity_result.get('url', '无')[:80]}...")
        print(f"  Parse: {unity_result.get('parse', '无')}")
        print(f"  Header: {'有' if unity_result.get('header') else '无'}")

        print(f"\n泥视频播放结果:")
        print(f"  URL: {nivod_result.get('url', '无')[:80]}...")
        print(f"  Parse: {nivod_result.get('parse', '无')}")
        print(f"  Header: {'有' if nivod_result.get('header') else '无'}")

        # 分析差异
        unity_url = unity_result.get('url', '')
        nivod_url = nivod_result.get('url', '')

        unity_parse = unity_result.get('parse', 1)
        nivod_parse = nivod_result.get('parse', 1)

        # 检查URL差异
        if unity_url and not nivod_url:
            issues.append("泥视频播放链接为空")
            solutions.append("修复泥视频的playerContent方法，确保返回有效的播放链接")

        # 检查parse字段差异
        if unity_parse != nivod_parse:
            issues.append(f"parse字段不一致: 统一影视({unity_parse}) vs 泥视频({nivod_parse})")
            solutions.append("统一parse字段设置，确保TVBox能正确处理")

        # 检查URL格式差异
        if unity_url and nivod_url:
            if unity_url.startswith('http') and not nivod_url.startswith('http'):
                issues.append("泥视频返回的不是完整的HTTP链接")
                solutions.append("确保泥视频返回完整的HTTP播放链接")

    # 检查数据结构差异
    unity_detail = unity_data.get('detail_structure', {})
    nivod_detail = nivod_data.get('detail_structure', {})

    # 检查关键字段完整性
    for field in ['vod_id', 'vod_name', 'vod_pic', 'vod_content']:
        unity_value = unity_detail.get(field, '')
        nivod_value = nivod_detail.get(field, '')

        if unity_value and not nivod_value:
            issues.append(f"泥视频缺少{field}字段")
            solutions.append(f"完善泥视频的{field}字段数据")

    # 输出问题和解决方案
    print(f"\n发现的问题:")
    print("-" * 60)
    if issues:
        for i, issue in enumerate(issues, 1):
            print(f"{i}. {issue}")
    else:
        print("✅ 未发现明显问题")

    print(f"\n解决方案:")
    print("-" * 60)
    if solutions:
        for i, solution in enumerate(solutions, 1):
            print(f"{i}. {solution}")
    else:
        print("✅ 无需特殊处理")

    # 生成具体的修复建议
    generate_fix_recommendations(issues, solutions)

def generate_fix_recommendations(issues, solutions):
    """生成具体的修复建议"""
    print(f"\n💡 具体修复建议:")
    print("-" * 60)

    if not issues:
        print("✅ 当前实现良好，无需修复")
        return

    print("基于对比分析，建议进行以下修复:")

    print("\n1. playerContent方法优化:")
    print("   - 确保返回完整的HTTP播放链接")
    print("   - 统一parse字段设置（建议设为0表示直接播放）")
    print("   - 添加适当的header信息")

    print("\n2. 数据结构完善:")
    print("   - 确保所有必需字段都有值")
    print("   - 统一字段格式和编码")
    print("   - 优化播放源和播放URL的组织结构")

    print("\n3. TVBox兼容性增强:")
    print("   - 参考统一影视的成功实现")
    print("   - 测试在TVBox中的实际表现")
    print("   - 确保跳转流程的完整性")

if __name__ == '__main__':
    analyze_tvbox_compatibility()
