# -*- coding: utf-8 -*-
"""
调试图片提取问题
"""

import sys
import requests
from lxml import etree

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def debug_image_extraction():
    """调试图片提取"""
    print("=" * 50)
    print("调试图片提取问题")
    print("=" * 50)
    
    try:
        # 获取首页HTML
        url = "https://www.nivod.vip/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        
        # 解析HTML
        doc = etree.HTML(response.text)
        
        # 查找所有包含 nivod 链接的元素
        video_links = doc.xpath('//a[contains(@href, "/nivod/")]')
        print(f"找到 {len(video_links)} 个视频链接")
        
        # 分析前10个链接的图片情况
        for i, link in enumerate(video_links[:10]):
            href = link.get('href', '')
            title = link.get('title', '') or link.text or ''
            
            print(f"\n视频 {i+1}: {title[:30]}...")
            print(f"  链接: {href}")
            
            # 查找图片
            img_elements = link.xpath('.//img')
            print(f"  找到 {len(img_elements)} 个图片元素")
            
            for j, img in enumerate(img_elements):
                src = img.get('src', '')
                data_src = img.get('data-src', '')
                data_original = img.get('data-original', '')
                alt = img.get('alt', '')
                
                print(f"    图片 {j+1}:")
                print(f"      src: {src}")
                print(f"      data-src: {data_src}")
                print(f"      data-original: {data_original}")
                print(f"      alt: {alt}")
                
                # 判断图片质量
                real_urls = [url for url in [src, data_src, data_original] if url and 'upload' in url and 'loading.png' not in url]
                if real_urls:
                    print(f"      状态: ✓ 真实图片 - {real_urls[0]}")
                elif any('loading.png' in url for url in [src, data_src, data_original] if url):
                    print(f"      状态: ⚠️ 占位符图片")
                else:
                    print(f"      状态: ✗ 无有效图片")
        
        # 专门查找包含真实图片的链接
        print(f"\n" + "=" * 50)
        print("查找包含真实图片的链接")
        print("=" * 50)
        
        real_image_links = doc.xpath('//a[contains(@href, "/nivod/") and .//img[contains(@src, "upload")]]')
        print(f"找到 {len(real_image_links)} 个包含真实图片的链接")
        
        for i, link in enumerate(real_image_links[:5]):
            href = link.get('href', '')
            title = link.get('title', '') or link.text or ''
            
            print(f"\n真实图片链接 {i+1}: {title[:30]}...")
            print(f"  链接: {href}")
            
            img = link.xpath('.//img[contains(@src, "upload")]')[0]
            src = img.get('src', '')
            print(f"  真实图片: {src}")
        
        # 分析HTML结构
        print(f"\n" + "=" * 50)
        print("分析HTML结构")
        print("=" * 50)
        
        # 查找推荐区域
        recommendation_areas = doc.xpath('//div[not(contains(@class, "hot")) and not(contains(text(), "正在热映"))]//a[contains(@href, "/nivod/")]')
        print(f"推荐区域链接数: {len(recommendation_areas)}")
        
        # 查找热映区域
        hot_areas = doc.xpath('//div[contains(@class, "hot") or contains(text(), "正在热映")]//a[contains(@href, "/nivod/")]')
        print(f"热映区域链接数: {len(hot_areas)}")
        
        # 分析不同区域的图片质量
        print(f"\n推荐区域图片质量分析:")
        real_pic_count = 0
        for link in recommendation_areas[:5]:
            img_elements = link.xpath('.//img')
            for img in img_elements:
                src = img.get('src', '')
                if src and 'upload' in src:
                    real_pic_count += 1
                    print(f"  ✓ 真实图片: {src}")
                elif src and 'loading.png' in src:
                    print(f"  ⚠️ 占位符: {src}")
        
        print(f"\n热映区域图片质量分析:")
        for link in hot_areas[:5]:
            img_elements = link.xpath('.//img')
            for img in img_elements:
                src = img.get('src', '')
                if src and 'upload' in src:
                    print(f"  ✓ 真实图片: {src}")
                elif src and 'loading.png' in src:
                    print(f"  ⚠️ 占位符: {src}")
        
        print(f"\n总结:")
        print(f"  总视频链接: {len(video_links)}")
        print(f"  包含真实图片的链接: {len(real_image_links)}")
        print(f"  推荐区域链接: {len(recommendation_areas)}")
        print(f"  热映区域链接: {len(hot_areas)}")
        
    except Exception as e:
        print(f"调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_image_extraction()
