# -*- coding: utf-8 -*-
"""
测试当前正则表达式是否能正确提取视频链接
"""

import re

def test_regex_patterns():
    """测试正则表达式模式"""
    print("=" * 60)
    print("测试视频链接提取正则表达式")
    print("=" * 60)
    
    # 从分析结果中获取的实际内容
    test_content = '''
    var player_aaaa={"flag":"play","encrypt":0,"trysee":0,"points":0,"link":"\/niplay\/84898-1-1\/","link_next":"","link_pre":"","vod_data":{"vod_name":"两个女人","vod_actor":"卡琳娜·卡普尔,阿琼·卡普尔,克里蒂·萨农,塔布,迪尔吉特·多桑,沙希德·卡普尔","vod_director":"拉杰·梅塔,DK","vod_content":"两个女人详情介绍-两个女人免费在线观看-两个女人下载"},"url":"https:\/\/hn.bfvvs.com\/play\/mbkzB4Xa\/index.m3u8","url_next":"","from":"play","server":"no","note":"","id":"84898","sid":1,"nid":1};
    '''
    
    # 当前使用的正则表达式模式
    current_patterns = [
        r'"url"\s*:\s*"([^"]+\.m3u8[^"]*)"',
        r'"src"\s*:\s*"([^"]+\.mp4[^"]*)"',
        r'video\s*:\s*"([^"]+)"',
        r'source\s*:\s*"([^"]+)"',
        r'https?://[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*'
    ]
    
    print("\n1. 测试当前正则表达式模式")
    print("-" * 40)
    
    for i, pattern in enumerate(current_patterns):
        print(f"\n模式 {i+1}: {pattern}")
        matches = re.findall(pattern, test_content, re.IGNORECASE)
        if matches:
            print(f"  匹配结果: {matches}")
            for match in matches:
                if match and ('http' in match or match.startswith('//')):
                    # 处理JSON转义字符
                    clean_url = match.replace('\\/', '/')
                    clean_url = clean_url.replace('\\"', '"').replace('\\\\', '\\')
                    print(f"  清理后: {clean_url}")
        else:
            print(f"  无匹配")
    
    print("\n2. 测试改进的正则表达式模式")
    print("-" * 40)
    
    # 改进的正则表达式模式
    improved_patterns = [
        r'"url"\s*:\s*"([^"]+)"',  # 更宽泛的url匹配
        r'"src"\s*:\s*"([^"]+)"',  # 更宽泛的src匹配
        r'https?:[^"\s]+\.(?:m3u8|mp4|flv)[^"\s]*',  # 支持转义的斜杠
        r'"([^"]*https?:[^"]*\.(?:m3u8|mp4|flv)[^"]*)"'  # 在引号内的完整URL
    ]
    
    for i, pattern in enumerate(improved_patterns):
        print(f"\n改进模式 {i+1}: {pattern}")
        matches = re.findall(pattern, test_content, re.IGNORECASE)
        if matches:
            print(f"  匹配结果: {matches}")
            for match in matches:
                if match and ('http' in match or 'https' in match):
                    # 处理JSON转义字符
                    clean_url = match.replace('\\/', '/')
                    clean_url = clean_url.replace('\\"', '"').replace('\\\\', '\\')
                    print(f"  清理后: {clean_url}")
                    
                    # 验证是否为有效的视频链接
                    if any(ext in clean_url.lower() for ext in ['.m3u8', '.mp4', '.flv']):
                        print(f"  ✓ 有效的视频链接")
                    else:
                        print(f"  ⚠️ 可能不是视频链接")
        else:
            print(f"  无匹配")
    
    print("\n3. 测试特定的player_aaaa解析")
    print("-" * 40)
    
    # 专门针对player_aaaa的解析
    player_pattern = r'var\s+player_aaaa\s*=\s*({.*?});'
    player_match = re.search(player_pattern, test_content, re.DOTALL)
    
    if player_match:
        player_json_str = player_match.group(1)
        print(f"找到player_aaaa配置:")
        print(f"  JSON字符串: {player_json_str[:100]}...")
        
        # 尝试解析JSON
        try:
            import json
            # 处理转义字符
            clean_json = player_json_str.replace('\\/', '/')
            player_data = json.loads(clean_json)
            
            if 'url' in player_data:
                video_url = player_data['url']
                print(f"  提取的视频URL: {video_url}")
                print(f"  ✓ 成功解析player_aaaa配置")
            else:
                print(f"  ⚠️ player_aaaa中没有url字段")
                
        except json.JSONDecodeError as e:
            print(f"  ✗ JSON解析失败: {str(e)}")
    else:
        print(f"未找到player_aaaa配置")
    
    print("\n4. 推荐的最佳提取方法")
    print("-" * 40)
    
    print("基于分析结果，推荐以下提取策略：")
    print("1. 优先解析player_aaaa等JavaScript变量")
    print("2. 使用改进的正则表达式匹配转义的URL")
    print("3. 对提取的URL进行转义字符清理")
    print("4. 验证URL的有效性")

if __name__ == "__main__":
    test_regex_patterns()
