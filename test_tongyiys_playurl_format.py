# -*- coding: utf-8 -*-
"""
测试统一影视爬虫的vod_play_url格式
详细分析播放URL的结构和格式
"""

import sys

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_tongyiys_playurl_format():
    """测试统一影视播放URL格式"""
    print("=" * 80)
    print("🔍 测试统一影视爬虫vod_play_url格式")
    print("=" * 80)
    
    # 导入统一影视爬虫
    try:
        from 统一影视 import Spider
        spider = Spider()
        spider.init()
        print("✅ 统一影视爬虫初始化成功")
        print(f"网站地址: {spider.host}")
    except Exception as e:
        print(f"❌ 统一影视爬虫初始化失败: {str(e)}")
        return
    
    # 1. 先搜索获取一个有效的视频ID
    print("\n1. 搜索获取测试视频ID")
    print("-" * 50)
    
    try:
        search_result = spider.searchContent("斗罗大陆", False, "1")
        if search_result and 'list' in search_result and search_result['list']:
            test_video = search_result['list'][0]
            test_id = test_video['vod_id']
            test_name = test_video['vod_name']
            print(f"✅ 找到测试视频: {test_name} (ID: {test_id})")
        else:
            print("❌ 搜索未找到测试视频，使用默认ID")
            test_id = "73268"  # 使用之前测试过的ID
            test_name = "测试视频"
    except Exception as e:
        print(f"❌ 搜索失败: {str(e)}")
        test_id = "73268"
        test_name = "测试视频"
    
    # 2. 测试detailContent方法，重点分析vod_play_url
    print(f"\n2. 分析detailContent返回的vod_play_url格式 - ID: {test_id}")
    print("-" * 50)
    
    try:
        detail_result = spider.detailContent([test_id])
        
        if detail_result and 'list' in detail_result and detail_result['list']:
            video = detail_result['list'][0]
            
            print(f"视频基本信息:")
            print(f"  ID: {video.get('vod_id', 'N/A')}")
            print(f"  标题: {video.get('vod_name', 'N/A')}")
            
            # 重点分析播放URL格式
            play_from = video.get('vod_play_from', '')
            play_url = video.get('vod_play_url', '')
            
            print(f"\n📺 播放URL格式详细分析:")
            print(f"vod_play_from: {play_from}")
            print(f"vod_play_url总长度: {len(play_url)}")
            
            if play_from and play_url:
                # 分析播放源
                sources = play_from.split('$$$')
                urls = play_url.split('$$$')
                
                print(f"\n播放源数量: {len(sources)}")
                print(f"播放URL组数量: {len(urls)}")
                
                # 详细分析每个播放源的URL格式
                for i, (source, url_group) in enumerate(zip(sources, urls)):
                    print(f"\n播放源 {i+1}: {source}")
                    print("-" * 40)
                    
                    # 分析剧集URL
                    episodes = url_group.split('#')
                    print(f"剧集数量: {len(episodes)}")
                    
                    # 分析前5集的URL格式
                    print(f"前5集URL格式分析:")
                    for j, episode in enumerate(episodes[:5]):
                        if '$' in episode:
                            ep_name, ep_url = episode.split('$', 1)
                            print(f"  第{j+1}集:")
                            print(f"    集名: {ep_name}")
                            print(f"    URL: {ep_url}")
                            
                            # 分析URL结构
                            if ep_url.startswith('/'):
                                print(f"    URL类型: 相对路径")
                                print(f"    完整URL: {spider.host}{ep_url}")
                            elif ep_url.startswith('http'):
                                print(f"    URL类型: 绝对路径")
                            else:
                                print(f"    URL类型: 其他格式")
                                
                            # 分析URL参数
                            if '/vod/play/' in ep_url:
                                print(f"    URL模式: 标准播放页面")
                                # 提取ID和SID
                                import re
                                id_match = re.search(r'/id/(\d+)', ep_url)
                                sid_match = re.search(r'/sid/(\d+)', ep_url)
                                nid_match = re.search(r'/nid/(\d+)', ep_url)
                                
                                if id_match:
                                    print(f"    视频ID: {id_match.group(1)}")
                                if sid_match:
                                    print(f"    播放源ID: {sid_match.group(1)}")
                                if nid_match:
                                    print(f"    剧集ID: {nid_match.group(1)}")
                            else:
                                print(f"    URL模式: 非标准格式")
                        else:
                            print(f"  第{j+1}集: {episode} (格式异常)")
                    
                    # 如果剧集很多，显示最后一集
                    if len(episodes) > 5:
                        print(f"  ... (省略{len(episodes)-6}集)")
                        last_episode = episodes[-1]
                        if '$' in last_episode:
                            ep_name, ep_url = last_episode.split('$', 1)
                            print(f"  最后一集:")
                            print(f"    集名: {ep_name}")
                            print(f"    URL: {ep_url}")
                
                # 总结URL格式特点
                print(f"\n🔍 URL格式特点总结:")
                sample_episode = urls[0].split('#')[0] if urls and '#' in urls[0] else ''
                if sample_episode and '$' in sample_episode:
                    _, sample_url = sample_episode.split('$', 1)
                    print(f"示例URL: {sample_url}")
                    
                    if '/vod/play/' in sample_url:
                        print("✅ 使用标准的/vod/play/路径")
                        print("✅ 包含id/sid/nid参数")
                        print("✅ 符合统一影视网站播放页面格式")
                    else:
                        print("❓ 非标准播放URL格式")
                        
                    if sample_url.startswith('/'):
                        print("✅ 使用相对路径，需要拼接host")
                    elif sample_url.startswith('http'):
                        print("✅ 使用绝对路径，可直接访问")
                        
            else:
                print("❌ 播放源或播放URL为空")
                
        else:
            print("❌ detailContent返回空结果")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试playerContent方法，看看它如何处理这些URL
    print(f"\n3. 测试playerContent方法处理URL")
    print("-" * 50)
    
    try:
        # 构造一个测试播放URL
        test_play_url = f"/index.php/vod/play/id/{test_id}/sid/1/nid/1.html"
        print(f"测试播放URL: {test_play_url}")
        
        # 调用playerContent方法
        player_result = spider.playerContent("", test_play_url, "")
        
        if player_result:
            print(f"playerContent返回结果:")
            print(f"  类型: {type(player_result)}")
            
            if isinstance(player_result, dict):
                for key, value in player_result.items():
                    if isinstance(value, str) and len(value) > 100:
                        print(f"  {key}: {value[:100]}...")
                    else:
                        print(f"  {key}: {value}")
            else:
                print(f"  内容: {str(player_result)[:200]}...")
        else:
            print("❌ playerContent返回空结果")
            
    except Exception as e:
        print(f"❌ playerContent测试失败: {str(e)}")
    
    print(f"\n📊 统一影视URL格式分析完成")
    print("=" * 50)

if __name__ == '__main__':
    test_tongyiys_playurl_format()
