# -*- coding: utf-8 -*-
"""
详细分析泥视频爬虫插件中播放源（vod_play_from）和播放链接（vod_play_url）的对应关系
对比统一影视的实现，识别潜在问题并提供解决方案
"""

import sys
import re

sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def analyze_correspondence_relationship():
    """分析播放源和播放链接的对应关系"""
    print("=" * 100)
    print("🔍 泥视频爬虫播放源与播放链接对应关系详细分析")
    print("=" * 100)
    
    # 测试视频列表
    test_cases = [
        {"id": "82135", "name": "斗罗大陆II绝世唐门", "type": "多集动漫"},
        {"id": "84898", "name": "两个女人", "type": "电影"},
    ]
    
    # 导入爬虫
    try:
        from 泥视频 import Spider as NivodSpider
        from 统一影视 import Spider as TongyiysSpider
        
        nivod_spider = NivodSpider()
        nivod_spider.init()
        
        tongyiys_spider = TongyiysSpider()
        tongyiys_spider.init()
        
        print("✅ 爬虫初始化成功")
        print(f"泥视频: {nivod_spider.host}")
        print(f"统一影视: {tongyiys_spider.host}")
        
    except Exception as e:
        print(f"❌ 爬虫初始化失败: {str(e)}")
        return
    
    # 分析每个测试视频
    for i, test_case in enumerate(test_cases):
        test_id = test_case["id"]
        test_name = test_case["name"]
        test_type = test_case["type"]
        
        print(f"\n{'='*80}")
        print(f"📺 测试视频 {i+1}: {test_name} ({test_type})")
        print(f"视频ID: {test_id}")
        print(f"{'='*80}")
        
        # 分析泥视频
        print(f"\n🎯 泥视频分析:")
        print("-" * 60)
        nivod_result = analyze_nivod_correspondence(nivod_spider, test_id, test_name)
        
        # 分析统一影视（如果有对应视频）
        print(f"\n🎯 统一影视对比分析:")
        print("-" * 60)
        tongyiys_result = analyze_tongyiys_correspondence(tongyiys_spider, test_name)
        
        # 对比分析
        print(f"\n📊 对比分析:")
        print("-" * 60)
        compare_correspondence(nivod_result, tongyiys_result, test_name)
    
    # 总结分析结果
    print(f"\n{'='*100}")
    print("📋 总结分析报告")
    print(f"{'='*100}")
    generate_analysis_report()

def analyze_nivod_correspondence(spider, test_id, test_name):
    """分析泥视频的对应关系"""
    try:
        detail_result = spider.detailContent([test_id])
        
        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return None
            
        video = detail_result['list'][0]
        play_from = video.get('vod_play_from', '')
        play_url = video.get('vod_play_url', '')
        
        print(f"原始数据:")
        print(f"  vod_play_from: {play_from}")
        print(f"  vod_play_url长度: {len(play_url)}")
        
        # 1. 检查数据结构对应性
        print(f"\n1️⃣ 数据结构对应性检查:")
        structure_result = check_data_structure_consistency(play_from, play_url)
        
        # 2. 分析播放源与URL映射关系
        print(f"\n2️⃣ 播放源与URL映射关系分析:")
        mapping_result = analyze_source_url_mapping(play_from, play_url)
        
        # 3. 验证URL格式正确性
        print(f"\n3️⃣ URL格式正确性验证:")
        format_result = verify_url_format(play_url)
        
        return {
            'spider_type': '泥视频',
            'video_id': test_id,
            'video_name': test_name,
            'play_from': play_from,
            'play_url': play_url,
            'structure': structure_result,
            'mapping': mapping_result,
            'format': format_result
        }
        
    except Exception as e:
        print(f"❌ 泥视频分析失败: {str(e)}")
        return None

def check_data_structure_consistency(play_from, play_url):
    """检查数据结构一致性"""
    if not play_from or not play_url:
        print("❌ 播放源或播放链接为空")
        return {'consistent': False, 'reason': '数据为空'}
    
    # 分割播放源和播放链接
    sources = play_from.split('$$$')
    url_groups = play_url.split('$$$')
    
    source_count = len(sources)
    url_group_count = len(url_groups)
    
    print(f"播放源数量: {source_count}")
    print(f"播放链接组数量: {url_group_count}")
    
    # 检查数量是否一致
    if source_count == url_group_count:
        print("✅ 播放源数量与播放链接组数量一致")
        consistent = True
        reason = "数量匹配"
    else:
        print(f"❌ 数量不一致！播放源{source_count}个，播放链接组{url_group_count}个")
        consistent = False
        reason = f"数量不匹配: {source_count} vs {url_group_count}"
    
    return {
        'consistent': consistent,
        'reason': reason,
        'source_count': source_count,
        'url_group_count': url_group_count,
        'sources': sources,
        'url_groups': url_groups
    }

def analyze_source_url_mapping(play_from, play_url):
    """分析播放源与URL映射关系"""
    if not play_from or not play_url:
        return {'valid': False, 'reason': '数据为空'}
    
    sources = play_from.split('$$$')
    url_groups = play_url.split('$$$')
    
    mapping_results = []
    
    # 分析每个播放源与对应URL组的关系
    for i, (source, url_group) in enumerate(zip(sources, url_groups)):
        print(f"\n播放源 {i+1}: {source}")
        print("-" * 40)
        
        # 分析剧集
        episodes = url_group.split('#') if url_group else []
        episode_count = len(episodes)
        
        print(f"剧集数量: {episode_count}")
        
        # 分析前3集和最后1集的格式
        sample_episodes = []
        if episodes:
            # 前3集
            for j, episode in enumerate(episodes[:3]):
                if '$' in episode:
                    ep_name, ep_url = episode.split('$', 1)
                    sample_episodes.append({
                        'index': j+1,
                        'name': ep_name,
                        'url': ep_url,
                        'position': '前部'
                    })
                    print(f"  第{j+1}集: {ep_name} -> {ep_url}")
            
            # 最后1集（如果总数大于3）
            if len(episodes) > 3:
                last_episode = episodes[-1]
                if '$' in last_episode:
                    ep_name, ep_url = last_episode.split('$', 1)
                    sample_episodes.append({
                        'index': len(episodes),
                        'name': ep_name,
                        'url': ep_url,
                        'position': '末尾'
                    })
                    print(f"  第{len(episodes)}集: {ep_name} -> {ep_url}")
        
        # 验证URL格式一致性
        url_formats = set()
        for episode in episodes[:5]:  # 检查前5集
            if '$' in episode:
                _, ep_url = episode.split('$', 1)
                if ep_url.startswith('/niplay/'):
                    url_formats.add('niplay_format')
                elif ep_url.startswith('/'):
                    url_formats.add('relative_path')
                elif ep_url.startswith('http'):
                    url_formats.add('absolute_url')
                else:
                    url_formats.add('unknown_format')
        
        format_consistency = len(url_formats) == 1
        print(f"URL格式一致性: {'✅ 一致' if format_consistency else '❌ 不一致'}")
        if url_formats:
            print(f"URL格式类型: {list(url_formats)}")
        
        mapping_results.append({
            'source_index': i+1,
            'source_name': source,
            'episode_count': episode_count,
            'sample_episodes': sample_episodes,
            'format_consistency': format_consistency,
            'url_formats': list(url_formats)
        })
    
    return {
        'valid': True,
        'mapping_count': len(mapping_results),
        'mappings': mapping_results
    }

def verify_url_format(play_url):
    """验证URL格式正确性"""
    if not play_url:
        return {'valid': False, 'reason': 'URL为空'}
    
    url_groups = play_url.split('$$$')
    format_results = []
    
    for i, url_group in enumerate(url_groups):
        episodes = url_group.split('#')
        
        # 检查URL格式
        niplay_count = 0
        total_count = len(episodes)
        
        for episode in episodes:
            if '$' in episode:
                _, ep_url = episode.split('$', 1)
                if '/niplay/' in ep_url:
                    niplay_count += 1
        
        niplay_ratio = niplay_count / total_count if total_count > 0 else 0
        
        format_results.append({
            'group_index': i+1,
            'total_episodes': total_count,
            'niplay_episodes': niplay_count,
            'niplay_ratio': niplay_ratio,
            'format_correct': niplay_ratio > 0.9  # 90%以上使用niplay格式认为正确
        })
        
        print(f"播放源{i+1}: {niplay_count}/{total_count} 使用niplay格式 ({niplay_ratio:.1%})")
    
    overall_valid = all(result['format_correct'] for result in format_results)
    
    return {
        'valid': overall_valid,
        'format_results': format_results,
        'summary': f"{'✅ 格式正确' if overall_valid else '❌ 格式有问题'}"
    }

def analyze_tongyiys_correspondence(spider, test_name):
    """分析统一影视的对应关系作为对比"""
    try:
        # 搜索对应视频
        search_result = spider.searchContent(test_name, False, "1")

        if not search_result or 'list' not in search_result or not search_result['list']:
            print("❌ 未找到对应视频")
            return None

        # 取第一个搜索结果
        video = search_result['list'][0]
        test_id = video['vod_id']
        found_name = video['vod_name']

        print(f"找到视频: {found_name} (ID: {test_id})")

        # 获取详情
        detail_result = spider.detailContent([test_id])

        if not detail_result or 'list' not in detail_result or not detail_result['list']:
            print("❌ 获取详情失败")
            return None

        video_detail = detail_result['list'][0]
        play_from = video_detail.get('vod_play_from', '')
        play_url = video_detail.get('vod_play_url', '')

        print(f"原始数据:")
        print(f"  vod_play_from: {play_from[:100]}...")
        print(f"  vod_play_url长度: {len(play_url)}")

        # 分析SID机制
        print(f"\n🔍 SID机制分析:")
        sid_analysis = analyze_sid_mechanism(play_from, play_url)

        return {
            'spider_type': '统一影视',
            'video_id': test_id,
            'video_name': found_name,
            'play_from': play_from,
            'play_url': play_url,
            'sid_analysis': sid_analysis
        }

    except Exception as e:
        print(f"❌ 统一影视分析失败: {str(e)}")
        return None

def analyze_sid_mechanism(play_from, play_url):
    """分析统一影视的SID机制"""
    if not play_from or not play_url:
        return {'valid': False, 'reason': '数据为空'}

    sources = play_from.split('$$$')
    url_groups = play_url.split('$$$')

    print(f"播放源数量: {len(sources)}")
    print(f"播放链接组数量: {len(url_groups)}")

    # 提取SID信息
    sid_info = []
    for i, source in enumerate(sources):
        # 提取SID
        sid_match = re.search(r'\[SID:(\d+)\]', source)
        if sid_match:
            sid = int(sid_match.group(1))
            source_name = source.split('[SID:')[0]
            sid_info.append({
                'index': i+1,
                'source_name': source_name,
                'sid': sid,
                'full_name': source
            })
            print(f"  播放源{i+1}: {source_name} (SID: {sid})")
        else:
            print(f"  播放源{i+1}: {source} (无SID)")

    # 分析URL中的SID使用
    if url_groups:
        sample_url_group = url_groups[0]
        episodes = sample_url_group.split('#')

        if episodes and '$' in episodes[0]:
            _, sample_url = episodes[0].split('$', 1)
            sid_in_url = re.search(r'/sid/(\d+)/', sample_url)

            if sid_in_url:
                url_sid = int(sid_in_url.group(1))
                print(f"URL中的SID: {url_sid}")

                # 检查SID是否匹配
                if sid_info and sid_info[0]['sid'] == url_sid:
                    print("✅ SID匹配正确")
                else:
                    print("❌ SID不匹配")
            else:
                print("❌ URL中未找到SID")

    return {
        'valid': True,
        'sid_count': len(sid_info),
        'sid_info': sid_info,
        'has_sid_in_url': 'sid_in_url' in locals()
    }

def compare_correspondence(nivod_result, tongyiys_result, test_name):
    """对比两个爬虫的对应关系"""
    print(f"对比视频: {test_name}")

    if not nivod_result:
        print("❌ 泥视频数据缺失，无法对比")
        return

    if not tongyiys_result:
        print("❌ 统一影视数据缺失，无法对比")
        return

    # 对比数据结构
    print(f"\n📊 数据结构对比:")
    nivod_structure = nivod_result.get('structure', {})

    print(f"泥视频:")
    print(f"  播放源数量: {nivod_structure.get('source_count', 0)}")
    print(f"  播放链接组数量: {nivod_structure.get('url_group_count', 0)}")
    print(f"  结构一致性: {'✅' if nivod_structure.get('consistent', False) else '❌'}")

    print(f"统一影视:")
    tongyiys_sources = tongyiys_result['play_from'].split('$$$') if tongyiys_result['play_from'] else []
    tongyiys_urls = tongyiys_result['play_url'].split('$$$') if tongyiys_result['play_url'] else []
    print(f"  播放源数量: {len(tongyiys_sources)}")
    print(f"  播放链接组数量: {len(tongyiys_urls)}")
    print(f"  结构一致性: {'✅' if len(tongyiys_sources) == len(tongyiys_urls) else '❌'}")

    # 对比URL格式
    print(f"\n🔗 URL格式对比:")
    nivod_format = nivod_result.get('format', {})
    print(f"泥视频: {nivod_format.get('summary', '未知')}")

    if tongyiys_urls:
        sample_episode = tongyiys_urls[0].split('#')[0] if '#' in tongyiys_urls[0] else tongyiys_urls[0]
        if '$' in sample_episode:
            _, sample_url = sample_episode.split('$', 1)
            if '/index.php/vod/play/' in sample_url:
                print("统一影视: ✅ 使用标准播放页面格式")
            else:
                print("统一影视: ❌ 格式异常")

    # 对比播放源命名
    print(f"\n🏷️ 播放源命名对比:")
    print(f"泥视频: {nivod_result['play_from']}")
    print(f"统一影视: {tongyiys_result['play_from'][:100]}...")

def generate_analysis_report():
    """生成分析报告"""
    print("🔍 关键发现:")
    print("1. 数据结构对应性")
    print("   - 泥视频: 播放源与播放链接组需要严格一一对应")
    print("   - 统一影视: 使用SID机制确保精确映射")

    print("\n2. URL格式差异")
    print("   - 泥视频: /niplay/{播放ID}/ 格式")
    print("   - 统一影视: /index.php/vod/play/id/{id}/sid/{sid}/nid/{nid}.html 格式")

    print("\n3. 播放源标识")
    print("   - 泥视频: 简单名称（自营1线、自营2线等）")
    print("   - 统一影视: 名称+SID标识（1080[SID:3]等）")

    print("\n💡 建议改进:")
    print("1. 为泥视频添加播放源ID标识机制")
    print("2. 优化播放源与URL的映射逻辑")
    print("3. 增强数据结构一致性验证")
    print("4. 统一URL格式标准")

if __name__ == '__main__':
    analyze_correspondence_relationship()
