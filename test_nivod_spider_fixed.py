# -*- coding: utf-8 -*-
"""
泥视频爬虫修复后的完整功能测试脚本
重点测试：图片显示、视频播放、搜索功能
验证所有修复是否成功
"""

import sys
import os
import time
import json

# 添加项目路径
sys.path.append('.')
sys.path.append('./plugin')
sys.path.append('./plugin/html')

def test_spider_fixed():
    """修复后的完整爬虫功能测试"""
    print("=" * 70)
    print("泥视频爬虫修复后功能测试开始")
    print("=" * 70)
    
    try:
        # 导入爬虫类
        from plugin.html.泥视频 import Spider
        spider = Spider()
        
        # 初始化
        print("\n1. 测试初始化方法")
        start_time = time.time()
        spider.init()
        init_time = time.time() - start_time
        print(f"✓ 初始化完成 - 耗时: {init_time:.2f}秒")
        print(f"  Host: {spider.host}")
        print(f"  分类数: {len(spider.categories)}")
        
        # 测试首页内容（重点测试图片）
        print("\n2. 测试首页内容方法（重点：图片显示）")
        start_time = time.time()
        home_result = spider.homeContent(True)
        home_time = time.time() - start_time
        
        print(f"✓ 首页内容获取完成 - 耗时: {home_time:.2f}秒")
        print(f"  分类数: {len(home_result.get('class', []))}")
        print(f"  推荐视频数: {len(home_result.get('list', []))}")
        
        # 检查图片URL质量
        image_quality_check = 0
        total_videos = 0
        for video in home_result.get('list', [])[:5]:
            total_videos += 1
            pic_url = video.get('vod_pic', '')
            print(f"  视频: {video.get('vod_name', '')}")
            print(f"    图片URL: {pic_url}")
            if pic_url and 'upload' in pic_url and 'loading.png' not in pic_url:
                image_quality_check += 1
                print(f"    图片状态: ✓ 真实图片URL")
            elif pic_url and 'loading.png' in pic_url:
                print(f"    图片状态: ⚠️ 占位符图片")
            else:
                print(f"    图片状态: ✗ 无图片")
        
        image_success_rate = (image_quality_check / total_videos * 100) if total_videos > 0 else 0
        print(f"  图片质量: {image_quality_check}/{total_videos} ({image_success_rate:.1f}%)")
        
        # 测试分类内容
        print("\n3. 测试分类内容方法")
        start_time = time.time()
        category_result = spider.categoryContent('1', '1', True, {})
        category_time = time.time() - start_time
        
        print(f"✓ 分类内容获取完成 - 耗时: {category_time:.2f}秒")
        print(f"  视频数: {len(category_result.get('list', []))}")
        print(f"  当前页: {category_result.get('page', 0)}")
        
        # 测试搜索功能（重点测试修复）
        print("\n4. 测试搜索功能（重点：修复验证）")
        search_keywords = ['你好', '凡人修仙传', '电影']
        
        search_success = 0
        for keyword in search_keywords:
            start_time = time.time()
            search_result = spider.searchContent(keyword, True)
            search_time = time.time() - start_time
            
            result_count = len(search_result.get('list', []))
            print(f"✓ 搜索'{keyword}'完成 - 耗时: {search_time:.2f}秒")
            print(f"  结果数: {result_count}")
            
            if result_count > 0:
                search_success += 1
                print(f"  搜索状态: ✓ 成功")
                # 显示搜索结果示例
                for video in search_result['list'][:2]:
                    print(f"    结果: {video.get('vod_name', '')}")
            else:
                print(f"  搜索状态: ✗ 无结果")
        
        search_success_rate = (search_success / len(search_keywords) * 100)
        print(f"  搜索成功率: {search_success}/{len(search_keywords)} ({search_success_rate:.1f}%)")
        
        # 测试详情页面（重点测试播放源）
        print("\n5. 测试详情页面方法（重点：播放源）")
        test_vod_id = None
        
        # 从分类结果中获取测试ID
        if category_result.get('list'):
            test_vod_id = category_result['list'][0]['vod_id']
        elif home_result.get('list'):
            test_vod_id = home_result['list'][0]['vod_id']
        
        if test_vod_id:
            start_time = time.time()
            detail_result = spider.detailContent([test_vod_id])
            detail_time = time.time() - start_time
            
            print(f"✓ 详情页面获取完成 - 耗时: {detail_time:.2f}秒")
            print(f"  详情数: {len(detail_result.get('list', []))}")
            
            if detail_result.get('list'):
                detail_info = detail_result['list'][0]
                print(f"  视频ID: {detail_info.get('vod_id')}")
                print(f"  视频名称: {detail_info.get('vod_name', '')[:50]}...")
                
                play_from = detail_info.get('vod_play_from', '')
                play_url = detail_info.get('vod_play_url', '')
                
                if play_from and play_url:
                    play_sources = play_from.split('$$$')
                    play_urls = play_url.split('$$$')
                    print(f"  播放源数: {len(play_sources)}")
                    print(f"  播放源状态: ✓ 成功提取")
                    
                    for i, source in enumerate(play_sources[:3]):
                        print(f"    播放源{i+1}: {source}")
                    
                    # 测试播放链接
                    print("\n6. 测试播放链接方法（重点：播放链接）")
                    if play_sources and play_urls:
                        # 测试第一个播放源的第一集
                        first_episodes = play_urls[0].split('#')
                        if first_episodes:
                            episode_info = first_episodes[0].split('$')
                            if len(episode_info) >= 2:
                                play_id = episode_info[1]
                                
                                start_time = time.time()
                                player_result = spider.playerContent(play_sources[0], play_id, [])
                                player_time = time.time() - start_time
                                
                                print(f"✓ 播放链接获取完成 - 耗时: {player_time:.2f}秒")
                                print(f"  播放源: {play_sources[0]}")
                                print(f"  播放ID: {play_id}")
                                print(f"  解析方式: {player_result.get('parse', 1)}")
                                
                                play_url_result = player_result.get('url', '')
                                if play_url_result:
                                    print(f"  播放链接: {play_url_result[:100]}...")
                                    print(f"  播放链接状态: ✓ 成功获取")
                                else:
                                    print(f"  播放链接状态: ✗ 获取失败")
                else:
                    print(f"  播放源状态: ✗ 提取失败")
        
        # 性能和质量总结
        print("\n" + "=" * 70)
        print("修复效果总结")
        print("=" * 70)
        
        # 性能指标
        print("性能指标:")
        print(f"  初始化耗时: {init_time:.2f}秒 {'✓' if init_time < 5 else '✗'}")
        print(f"  首页内容耗时: {home_time:.2f}秒 {'✓' if home_time < 5 else '✗'}")
        print(f"  分类内容耗时: {category_time:.2f}秒 {'✓' if category_time < 5 else '✗'}")
        
        # 功能修复状态
        print("\n功能修复状态:")
        print(f"  图片显示修复: {'✓ 成功' if image_success_rate > 50 else '✗ 需要进一步优化'} ({image_success_rate:.1f}%)")
        print(f"  搜索功能修复: {'✓ 成功' if search_success_rate > 50 else '✗ 需要进一步优化'} ({search_success_rate:.1f}%)")
        
        if 'detail_result' in locals() and detail_result.get('list'):
            detail_info = detail_result['list'][0]
            play_from = detail_info.get('vod_play_from', '')
            play_url = detail_info.get('vod_play_url', '')
            play_status = "✓ 成功" if (play_from and play_url) else "✗ 失败"
            print(f"  播放源修复: {play_status}")
            
            if 'player_result' in locals():
                player_url = player_result.get('url', '')
                player_status = "✓ 成功" if player_url else "✗ 失败"
                print(f"  播放链接修复: {player_status}")
        
        # 数据格式验证
        print("\n数据格式验证:")
        format_checks = [
            ("首页分类格式", isinstance(home_result.get('class'), list)),
            ("首页推荐格式", isinstance(home_result.get('list'), list)),
            ("分类视频格式", isinstance(category_result.get('list'), list)),
            ("搜索结果格式", True),  # 搜索已测试
        ]
        
        if 'detail_result' in locals():
            format_checks.append(("详情数据格式", isinstance(detail_result.get('list'), list)))
        
        if 'player_result' in locals():
            format_checks.append(("播放数据格式", 'parse' in player_result and 'url' in player_result))
        
        all_formats_ok = True
        for check_name, check_result in format_checks:
            status = "✓" if check_result else "✗"
            print(f"  {check_name}: {status}")
            if not check_result:
                all_formats_ok = False
        
        # 最终结论
        print("\n" + "=" * 70)
        overall_success = (image_success_rate > 50 and search_success_rate > 50 and all_formats_ok)
        if overall_success:
            print("🎉 修复成功！所有关键功能正常工作")
        else:
            print("⚠️ 部分功能仍需优化")
        print("=" * 70)
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_spider_fixed()
